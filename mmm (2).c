//Copyright stuff goes here
#include <windows.h>
#include <stdint.h>
#include <stdbool.h>

//#define LAYERS 512
#define LAYERS 8

int _fltused;

//3DS data types
typedef int8_t s8;
typedef uint8_t u8;
typedef int16_t s16;
typedef uint16_t u16;
typedef int32_t s32;
typedef uint32_t u32;
typedef int64_t s64;
typedef uint64_t u64;

typedef u64 midi_tick_t;

struct Track
{
    u8* ptrs;
    u8* ptre;
    midi_tick_t nextcounter;
    u32 trackid;
    u8 cmd;
    u8 prm1;
    u8 prm2;
    u8 sbz;
};

//Startup stuff
static HANDLE heap = 0;
static HANDLE stdout = 0;
static HANDLE stdconout = 0;

#define USE_BHEAP
#define USE_PREFETCH
#define MIDI_MMIO

#ifdef USE_BHEAP
// return true if left goes lower than right
static bool track_cmp(const struct Track* __restrict left, const struct Track* __restrict right)
{
    if(left->nextcounter < right->nextcounter)
        return false;
    
    if(left->nextcounter == right->nextcounter)
        return left->trackid >= right->trackid;
    
    return true;
}

#define HEAP_NO_CHECKS
#define HEAP_FNPRIV static
#define HEAP_FNPUB static
#define HEAP_PREFIX b
#define HEAP_TYPE struct Track*
// return left >= right
#define HEAP_CMP(x, y) track_cmp(x, y)
#include "heap.h"
#endif

#ifdef _MSC_VER
#define __builtin_bswap16 _byteswap_ushort
#define __builtin_bswap32 _byteswap_ulong
#define _unreachable() __assume(0)
#else
#define _unreachable() __builtin_unreachable()
#endif

#ifdef USE_PREFETCH
#ifdef _MSC_VER
#define COMPILER_PLD(addr) _mm_prefetch(addr, 0)
#else
#define COMPILER_PLD(addr) __builtin_prefetch(addr, 0, 0)
#endif
#else
// Do nothing
#define COMPILER_PLD(addr)
#endif


static __declspec(noinline) BOOL writecon(const char* ch)
{
    DWORD chlen = 0;
    const char* cc = ch;
    while(*(cc++)) chlen++;
    return WriteFile(stdout, ch, chlen, &chlen, NULL);
}

#pragma optimize( "g", off )

static __declspec(noinline) void zeromem(void* ptr, DWORD size)
{
    size >>= 2;
    u32* up = (void*)((size_t)ptr & ~3);
    while(size--)
        *(up++) = 0;
}

static __declspec(noinline) void copymem(void* dst, const void* src, DWORD size)
{
    size >>= 2;
    u32* us = (void*)((size_t)src & ~3);
    u32* ud = (void*)((size_t)dst & ~3);
    while(size--)
        *(ud++) = *(us++);
}

#pragma optimize( "", on )

#ifdef MIDI_DEBUG
#include "exch.h"
#else
const char hex[] = "0123456789ABCDEF";
static __declspec(noinline) void hex32(u32 val)
{
    char buf[8];
    buf[7] = hex[val & 0xF];
    val >>= 4;
    buf[6] = hex[val & 0xF];
    val >>= 4;
    buf[5] = hex[val & 0xF];
    val >>= 4;
    buf[4] = hex[val & 0xF];
    val >>= 4;
    buf[3] = hex[val & 0xF];
    val >>= 4;
    buf[2] = hex[val & 0xF];
    val >>= 4;
    buf[1] = hex[val & 0xF];
    val >>= 4;
    buf[0] = hex[val & 0xF];
    DWORD written;
    WriteFile(stdout, buf, 8, &written, NULL);
}
#endif

static __declspec(noinline) void int64(u64 valin)
{
    DWORD written = 0;
    int tck = 0;
    char charbuf[24];
    zeromem(charbuf, 24);
    while(1)
    {
        charbuf[tck++] = hex[valin % 10];
        
        valin /= 10;
        if(!valin) break;
        
        if((tck % 4) == 3)
            charbuf[tck++] = ',';
    }
    while(tck < 18)
        charbuf[tck++] = ' ';
    while(tck--)
        WriteFile(stdout, charbuf + tck, 1, &written, NULL);
}

static __declspec(noinline) void mfint64(u64 valin)
{
    DWORD written = 0;
    int tck = 0;
    char charbuf[24];
    zeromem(charbuf, 24);
    while(1)
    {
        charbuf[tck++] = hex[valin % 10];
        
        valin /= 10;
        if(!valin) break;
    }
    while(tck--)
        WriteFile(stdout, charbuf + tck, 1, &written, NULL);
}

static __declspec(noinline) void time64(u64 valin)
{
    u32 val;
    
    val = valin / (1e7 * 60 * 60 * 24);
    if(val) { mfint64(val); writecon("d "); }
    val = valin / (1e7 * 60 * 60);
    if(val) { mfint64(val % 24); writecon("h "); }
    val = valin / (1e7 * 60);
    if(val) { mfint64(val % 60); writecon("m "); }
    val = (u64)(valin / (1e7)) % 60;
    mfint64(val); writecon("s ");
    //mfint64(valin % (u32)1e7); writecon("t");
}

__declspec(noinline) u8* varlen_decode_slow(u8* ptr, u32* __restrict out, u8 data)
{
    u32 result = (data & 0x7F) << 7;
    
    data = *(ptr++); if(data < 0x80) {goto done;}
    result = (result + (data & 0x7F)) << 7;
    data = *(ptr++); if(data < 0x80) {goto done;}
    result = (result + (data & 0x7F)) << 7;
    //WTF, varlen is undefined above 28bits, it's invalid to have bit7 set for the 4th byte
    data = *(ptr++); goto done;
    
done:
    *out = result + data;
    return ptr; 
}

__forceinline u8* varlen_decode(u8* ptr, u32* __restrict out)
{
    u8 data = *(ptr++);
    if(data < 0x80)
    {
        *out = data;
        return ptr;
    }
    
    return varlen_decode_slow(ptr, out, data);
}

__declspec(noinline) static void merge_tracks(struct Track* __restrict ptr)
{
    for(;;)
    {
        *ptr = *(ptr + 1);
        if(!ptr->ptrs)
            return;
        ++ptr;
    }
}

int main(int argc, wchar_t** argv);
void mainCRTStartup(void)
{
    heap = HeapCreate(5, 0, 0);
    if(!heap)
        ExitProcess(1);
    
    stdout = GetStdHandle(-12);
    stdconout = stdout;
    
#ifdef MIDI_DEBUG
    SetUnhandledExceptionFilter(crashhandler);
#endif
    
    
    int argc = 0;
    wchar_t** argv = 0;
    if(1)
    {
        wchar_t* cmdline = GetCommandLineW();
        wchar_t**(WINAPI*argparse)(wchar_t* cmdline, int* outcnt) = 0;
        HMODULE shl = GetModuleHandleA("shell32");
        if(!shl) shl = LoadLibraryA("shell32");
        if(shl)
        {
            argparse = (void*)GetProcAddress(shl, "CommandLineToArgvW");
            if(argparse)
                argv = argparse(cmdline, &argc);
        }
        if(!argparse)
            ExitProcess(1);
    }
    
    int ret = main(argc, argv);
    
    HeapDestroy(heap);
    ExitProcess(ret);
}
void __main(void)
{
    //empty
}

//Main stuff

#define SAMPLEFREQ 32
const int audiorate = 48000;
const int samplefreq = SAMPLEFREQ;
const float tickrate = 48000.0F / 1e7f;

static HANDLE f = 0;
#ifdef MIDI_MMIO
static HANDLE filemap = 0;
#endif

LPVOID gsyn;

int(__cdecl*syn_mix)(LPVOID, LPVOID, DWORD);
int(__cdecl*syn_msgshort)(LPVOID, DWORD);

void KShortMsg(DWORD msg)
{
    syn_msgshort(gsyn, msg);
}

int KModMessage(UINT devid, UINT msg, DWORD_PTR dwUser, DWORD_PTR dwParam1, DWORD_PTR dwParam2)
{
    return 0;
}

static u8* gptr = 0;
static HANDLE fo = 0;

static float samplebuf[4096];

int main(int argc, wchar_t** argv)
{
    writecon("MorshuMidi by Sono (build " DATETIME ")\n(C) 2018 All Rights Reserved\n\n");
    
    if(argc < 2)
    {
        writecon("Missing filename\n");
        return 1;
    }
    
    u8* ptr = 0;
    u8* ptrend = 0;
    do
    {
        f = CreateFileW(argv[1], GENERIC_READ, FILE_SHARE_READ, NULL, OPEN_EXISTING,
            FILE_ATTRIBUTE_NORMAL |
#ifdef MIDI_MMIO
            /*FILE_FLAG_RANDOM_ACCESS |*/ FILE_FLAG_SEQUENTIAL_SCAN,
#else
            FILE_FLAG_SEQUENTIAL_SCAN,
#endif
            NULL);
        if(!f)
        {
            writecon("Failed to open input file\n");
            goto unloadfail;
        }
        
        LARGE_INTEGER fs;
        fs.LowPart = 0;
        fs.HighPart = 0;
        
        fs.LowPart = GetFileSize(f, &fs.HighPart);
        
        if(GetLastError() ||
#ifdef MIDI_LEGACY
        fs.HighPart ||
#endif
        (!fs.HighPart && fs.LowPart < 0x19))
        {
            CloseHandle(f);
            writecon("Invalid file size\n");
            return 1;
        }
        
#ifdef MIDI_MMIO
        filemap = CreateFileMappingA(f, NULL, PAGE_READONLY, 0, 0, NULL);
        if(!filemap)
        {
            CloseHandle(f);
            writecon("mmap failed\n");
            goto unloadfail;
            //return 1;
        }
        
        ptr = MapViewOfFile(filemap, FILE_MAP_COPY, 0, 0, 0);
#else
        ptr = HeapAlloc(heap, 5,
#ifdef MIDI_LEGACY
        fs.LowPart
#else
        fs.QuadPart
#endif
        );
#endif
        if(!ptr)
        {
#ifdef MIDI_MMIO
            CloseHandle(filemap);
#endif
            CloseHandle(f);
            writecon("Memory allocation failure\n");
            goto unloadfail;
            //return 1;
        }
        gptr = ptr;
        
#ifdef MIDI_MMIO
        ptrend = ptr;
        ptrend +=
#ifdef MIDI_LEGACY
        fs.LowPart
#else
        fs.QuadPart
#endif
        ;
#else
        u8* dstptr = ptr;
        
        while(
#ifdef MIDI_LEGACY
        fs.LowPart
#else
        fs.QuadPart
#endif
        )
        {
            DWORD mustread = (fs.HighPart || (fs.LowPart >> 30)) ? 0x40000000 : fs.LowPart;
            if(!ReadFile(f, dstptr, mustread, &mustread, NULL))
            {
                writecon("File read failure\n");
                goto unloadfail;
            }
            
#ifdef MIDI_LEGACY
            fs.LowPart
#else
            fs.QuadPart
#endif
            -= mustread;
            dstptr += mustread;
        }
        
        #ifndef MIDI_MMIO
        CloseHandle(f);
        f = 0;
#endif
        
        ptrend = dstptr;
        
#endif
        fo = GetStdHandle(-11);//CreateFileA("_midiout.pcm", GENERIC_WRITE, 0, NULL, CREATE_ALWAYS, FILE_ATTRIBUTE_NORMAL, NULL);
        if(!fo)
            goto unloadfail;
    }
    while(0);
    
    if(*(u32*)ptr != *(u32*)"MThd")
    {
        writecon("No MThd\n");
        return 1;
    }
    
    u32 trkcnt = __builtin_bswap32(*(u32*)(ptr + 4));
    if(trkcnt < 6)
    {
        writecon("Invalid MThd chunk size\n");
        return 1;
    }
    
    if(__builtin_bswap16(*(u16*)(ptr + 8)) > 1)
    {
        writecon("Unsupported MIDI version\n");
        return 1;
    }
    
    u16 timediv = __builtin_bswap16(*(u16*)(ptr + 0xA));
    
    u8* mbuf = ptr + trkcnt + 8;
    trkcnt = 0;
    
    while(mbuf < ptrend)
    {
        if(!timediv) break;
        
        if(*(u32*)mbuf == *(u32*)"MTrk")
        {
            trkcnt++;
            timediv--;
        }
        
        mbuf += __builtin_bswap32(*(u32*)(mbuf + 4));
        mbuf += 8;
    }
    
    struct Track* tracks = HeapAlloc(heap, 0xD, sizeof(struct Track) * (trkcnt + 1));
    zeromem(tracks, sizeof(struct Track) * (trkcnt + 1));
    
    u32 onehz = 0;
    
    u32 samplepos = 0;
    
    mbuf = ptr + __builtin_bswap32(*(u32*)(ptr + 4)) + 8;
    timediv = 0;
    while(mbuf < ptrend)
    {
        if(trkcnt == timediv) break;
        
        if(*(u32*)mbuf == *(u32*)"MTrk")
        {
            tracks[timediv].ptrs = mbuf + 8;
            tracks[timediv].trackid = timediv;
            
            mbuf += __builtin_bswap32(*(u32*)(mbuf + 4));
            mbuf += 8;
            
            tracks[timediv].ptre = mbuf;
            
            timediv++;
        }
        else
        {
            mbuf += __builtin_bswap32(*(u32*)(mbuf + 4));
            mbuf += 8;
        }
    }
    
    HMODULE ks = 0;
    if(!ks) ks = LoadLibraryA("C:\\Data\\lolol\\Sono.SynthRender\\syn\\out\\syndrv.dll.dll");
    if(!ks) ks = LoadLibraryA("syndrv");
    if(!ks) ks = LoadLibraryA("syndrv\\syndrv");
    
    if(!ks) goto unloadfail;
    
    do
    {
        LPVOID(__cdecl*syn_alloc)(void) = (LPVOID)GetProcAddress(ks, "syn_alloc");
        void(__cdecl*syn_free)(LPVOID) = (LPVOID)GetProcAddress(ks, "syn_free");
        
        BOOL(__cdecl*syn_init)(LPVOID, DWORD) = (LPVOID)GetProcAddress(ks, "syn_init");
        void(__cdecl*syn_deinit)(LPVOID) = (LPVOID)GetProcAddress(ks, "syn_deinit");
        
        syn_mix = (LPVOID)GetProcAddress(ks, "syn_mix");
        
        syn_msgshort = (LPVOID)GetProcAddress(ks, "syn_msgshort");
        
        gsyn = syn_alloc();
        if(gsyn)
        {
            if(syn_init(gsyn, LAYERS))
            {
                break;
            }
            
            syn_deinit(gsyn);
            syn_free(gsyn);
        }
        
        goto unloadfail;
    }
    while(0);
    
    
    timediv = __builtin_bswap16(*(u16*)(ptr + 0xC));
    
    u32 tempomulti = (u32)(5000000 / timediv);
    midi_tick_t counter = 0;
    u32 minsleep = -1;
    u64 realtime = 0;
    
    struct Track* trk = tracks;
    
    for(;;)
    {
        if(!trk->ptrs) break;
        
        u8* ptrs = trk->ptrs;
        u8* ptre = trk->ptre;
        u32 slep;
        ptrs = varlen_decode(ptrs, &slep);
        
        if(ptrs < ptre)
        {
            trk->ptrs = ptrs;
            trk->nextcounter = slep;
            trk++;
            continue;
        }
        else
        {
            merge_tracks(trk);
            continue;
        }
    }
    
#ifdef USE_BHEAP
    uint32_t track_cnt = trkcnt;
    struct Track* __restrict *trk_popped = VirtualAlloc(NULL, track_cnt * sizeof(struct Track*), MEM_COMMIT | MEM_RESERVE, PAGE_READWRITE);
    uint32_t trk_popped_cnt = 0;
    
    bheap* lst = HeapAlloc(heap, 5, bheap_alloc_size(track_cnt));
    lst->count = 0;
    lst->size = track_cnt;
    
    {
        struct Track* __restrict trk2 = tracks;
        uint32_t add_begin = bheap_add_begin(lst);
        while(trk2 < trk)
        {
            if(!trk2->ptrs)
                break;
            
            bheap_add(lst, trk2);
            ++trk2;
        }
        
#ifdef BHEAP_DEBUG
        writecon("\nInitialization (pre):\n");
        bheap_print(lst);
#endif
        
        bheap_add_end(lst, add_begin);
        
#ifdef BHEAP_DEBUG
        writecon("\nInitialization (post):\n");
        bheap_print(lst);
#endif
    }
#endif
    
    for(;;)
    {
        trk = tracks;
        
        for(;;)
        {
#ifndef USE_BHEAP
            if(!trk->ptrs)
                break;
            
            if(trk->nextcounter > counter)
            {
                trk++;
                continue;
            }
#else
            struct Track** h = bheap_peek(lst);
            if(h)
                trk = *h;
            else
                break;
            
            if(trk->nextcounter <= counter)
                ;
            else
                break;
            
            bheap_pop(lst, NULL); // Always succeeds here
#endif
            
            u8* __restrict ptrs = trk->ptrs;
            COMPILER_PLD(ptrs);
            u8* ptre = trk->ptre;
            u32 slep = 0;
            
            union
            {
                struct { u8 cmd, prm1, prm2, sbz; };
                u32 dwMsg;
            } msg;
            msg.dwMsg = trk->cmd;
            
            do
            {
                u8 v = *ptrs;
                if(v >= 0x80)
                {
                    msg.cmd = v;
                    ptrs++;
                }
                
                u8 swcmd = msg.cmd;
                
                if(swcmd < 0xA0)
                {
                    if(swcmd >= 0x90)
                    {
                        msg.prm1 = *(ptrs++);
                        msg.prm2 = *(ptrs++);
                        
                        if(msg.prm2 != 1)
                            KShortMsg(msg.dwMsg);
                        
                        goto cmdend;
                    }
                    else
                    {
                    msg_2b:
                        msg.prm1 = *(ptrs++);
                        msg.prm2 = *(ptrs++);
                        
                        KShortMsg(msg.dwMsg);
                        
                        goto cmdend;
                    }
                }
                
                if(swcmd < 0xC0)
                {
                    goto msg_2b;
                }
                
                if(swcmd < 0xE0)
                {
                    msg.prm1 = *(ptrs++);
                    msg.prm2 = 0;
                    
                    KShortMsg(msg.dwMsg);
                    
                    goto cmdend;
                }
                
                if(swcmd < 0xF0)
                {
                    goto msg_2b;
                }
                
                if(swcmd == 0xFF)
                {
                    u8 meta = *(ptrs++);
                    
                    u32 metalen;
                    ptrs = varlen_decode(ptrs, &metalen);
                    
                    if(meta < 10)
                    {
                        //TODO print text
                    }
                    if(meta == 0x2F)
                    {
                        goto track_merge;
                    }
                    if(meta == 0x51)
                    {
                        u32 newtick = (ptrs[0] << 16) | (ptrs[1] << 8) | (ptrs[2] << 0);
                        tempomulti = (newtick * 10) / timediv;
                    }
                    
                    ptrs += metalen;
                    goto cmdend;
                }
                if(swcmd == 0xF0)
                {
                    u32 metalen;
                    ptrs = varlen_decode(ptrs, &metalen);
                    
                    *(ptrs - 1) = 0xF0;
                    MIDIHDR hdr;
                    zeromem(&hdr, sizeof(hdr));
                    hdr.lpData = ptrs - 1;
                    hdr.dwBufferLength = metalen + 1;
                    hdr.dwBytesRecorded = metalen + 1;
                    hdr.dwFlags = 2;
                    //while(KModMessage(0, 8, 0, (DWORD_PTR)&hdr, 0) == 67);
                    
                    ptrs += metalen;
                    goto cmdend;
                }
                
                writecon("Bypass\ncmd=");
                hex32(swcmd);
                writecon(" msg.cmd=");
                hex32(msg.cmd);
                writecon(" msg=");
                hex32(*(DWORD*)&msg);
                
                _unreachable();
                
                cmdend:
                
                COMPILER_PLD(ptrs + 16);
                
                slep = *(ptrs++);
                if(!slep)
                    continue;
        
                if(ptrs < ptre)
                {
                    --ptrs;
                    ptrs = varlen_decode(ptrs, &slep);
                    
                    midi_tick_t nextctr = trk->nextcounter + slep;
                    trk->nextcounter = nextctr;
                    
                    if(nextctr <= counter)
                        continue;
                    
                    trk->ptrs = ptrs;
                    trk->cmd = msg.cmd;
                    
                    goto track_next;
                }
                else
                {
                    goto track_merge;
                }
            }
            while(1);
            
            _unreachable();
            
            track_next:
#ifndef USE_BHEAP
            trk++;
#else
            trk_popped[trk_popped_cnt++] = trk;
#endif
            continue;
            
            track_merge:
            
#ifndef USE_BHEAP
            merge_tracks(trk);
#endif
            continue;
        }
        
#ifndef USE_BHEAP
        trk = tracks;
        
        for(;;)
        {
            if(!trk->ptrs)
            {
                if(minsleep >> 31)
                    trk = tracks;
                break;
            }
            
            if(trk->nextcounter <= counter)
            {
                writecon("Error: counter >= nextcounter\n");
            }
            
            u32 diff = trk->nextcounter - counter;
            
            if(diff >> 31)
            {
                writecon("Error: nextcounter - counter overflow\n");
            }
            
            if(diff < minsleep)
                minsleep = diff;
            
            trk++;
        }
        
        if(trk == tracks)
            break;
#else
        
        #ifdef BHEAP_DEBUG
        writecon("\nAfter loop:\n");
        bheap_print(lst);
#endif
        
        uint32_t lst_restore = bheap_add_begin(lst);
        for(u32 i = 0; i < trk_popped_cnt; i++)
            bheap_add(lst, trk_popped[i]);
        trk_popped_cnt = 0;
        bheap_add_end(lst, lst_restore);
        
#ifdef BHEAP_DEBUG
        writecon("\nRe-added:\n");
        bheap_print(lst);
#endif
        
        {
            struct Track** bhp = bheap_peek(lst);
            if(!bhp)
                break;
            
            trk = *bhp;
        }
        
        minsleep = trk->nextcounter - counter;
        
#ifdef BHEAP_DEBUG
        writecon("\nSleep: ");
        int64(trk->time);
        writecon(" - ");
        int64(counter);
        writecon(" = ");
        int64(minsleep);
        writecon("\n");
#endif
        
#endif
        
        if(minsleep)
        {
            counter += minsleep;
            u32 sleeptime = (u32)(minsleep * tempomulti);
            realtime += sleeptime;
            onehz += sleeptime;
            u32 sp = (u32)(realtime * tickrate);
            if((sp - samplepos) >= samplefreq)
            {
                DWORD written;
                //float samplebuf[512];
                do
                {
                    DWORD mustwrite = sizeof(samplebuf) / sizeof(samplebuf[0]);
                    if((sp - samplepos) < mustwrite)
                        mustwrite = sp - samplepos;
                    
                    if(mustwrite < samplefreq)
                        mustwrite = samplefreq;
                    
                    int synwritten = syn_mix(gsyn, samplebuf, mustwrite
                    //| (1 << 31)
                    );
                    if(synwritten > 0)
                        WriteFile(fo, samplebuf, (DWORD)synwritten * sizeof(float), &written, NULL);
                    samplepos += synwritten;
                }
                while((sp - samplepos) >= samplefreq);
                
                if(samplepos >> 31)
                {
                    sp ^= (1 << 31);
                    samplepos ^= (1 << 31);
                }
            }
            
            if(onehz >= 10000000)
            {
                writecon("DEBUG ");
                time64(realtime);
                writecon(" \r");
                onehz = onehz % 10000000;
            }
        }
        else
        {
            writecon("\nMinsleep is zero\n");
        }
        
        minsleep = -1;
        
        //writecon("----------------\n");
    }
    
    
    {
        DWORD remain = audiorate >> 1;
        DWORD written;
        //float samplebuf[512];
        for(;;)
        {
            DWORD mustwrite = 128;
            
            int synwritten = syn_mix(gsyn, samplebuf, mustwrite
            //| (1 << 31)
            );
            if(synwritten > 0)
            {
                float iamul = (remain << 1) / (float)audiorate;
                DWORD ia;
                for(ia = 0; ia != synwritten; ia++)
                    samplebuf[ia] *= iamul;
                
                WriteFile(fo, samplebuf, (DWORD)synwritten * sizeof(float), &written, NULL);
                
                if(synwritten < remain)
                {
                    remain -= synwritten;
                    continue;
                }
                
                break;
            }
            else
                break;
            
            samplepos += synwritten;
        }
    }
    
    //writecon("\r\n");
    
    //if(KSDeinit) KSDeinit();
    
#ifdef MIDI_MMIO
    UnmapViewOfFile(gptr);
    CloseHandle(filemap);
#endif
    CloseHandle(f);
    //CloseHandle(fo);
    
    return 0;
    
    unloadfail:
    
    do
    {
        char errbuf[256];
        FormatMessageA(FORMAT_MESSAGE_FROM_SYSTEM, 0, GetLastError(),
            MAKELANGID(LANG_NEUTRAL, SUBLANG_DEFAULT), errbuf, 255, NULL);
        errbuf[255] = 0;
        writecon(errbuf);
    }
    while(0);
    
#ifdef MIDI_MMIO
    UnmapViewOfFile(gptr);
    CloseHandle(filemap);
#endif
    CloseHandle(f);
    
    return 1;
}
