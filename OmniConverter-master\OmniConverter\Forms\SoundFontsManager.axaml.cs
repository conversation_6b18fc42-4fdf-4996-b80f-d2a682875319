using Avalonia.Controls;
using Avalonia.Input;
using Avalonia.Interactivity;
using Avalonia.Platform.Storage;
using ManagedBass.Midi;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;

namespace OmniConverter;

public partial class SoundFontsManager : Window
{
    public SoundFontsManager()
    {
        InitializeComponent();

        AddHandler(DragDrop.DropEvent, FileDropInit);
        AddHandler(DragDrop.DragEnterEvent, FileDropEnter);
        // AddHandler(DragDrop.DragLeaveEvent, FileDropLeave);

        try
        {
            SoundFontListView.ItemsSource = Program.SoundFontsManager.GetSoundFontList();
        }
        catch { }

        switch (Program.Settings.Renderer)
        {
            case EngineID.BASS:
                break;

            default:
                LinAttMod.IsVisible = false;
                LinDecVol.IsVisible = false;
                MinFx.IsVisible = false;
                EnforceSBLimits.IsVisible = false;
                NoRampIn.IsVisible = false;
                break;
        }
    }

    private void AddSFCheck(IEnumerable<IStorageItem>? files, bool dragndrop = false)
    {
        if (files == null)
            return;

        if (files.Count() >= 1)
        {
            List<string> filenames = new();

            foreach (var file in files)
            {
                var p = file.TryGetLocalPath();
                if (p != null) filenames.Add(p);
            }

            if (filenames.Count < 1)
                return;

            if (!dragndrop)
            {
                var folder = Path.GetDirectoryName(filenames[0]);

                if (folder != null)
                {
                    Program.Settings.LastSoundFontFolder = folder;
                    Program.SaveConfig();
                }
            }

            foreach (var filename in filenames)
            {
                var err = BassMidi.FontInit(filename, FontInitFlags.Unicode);

                if (err != 0)
                {
                    BassMidi.FontFree(err);
                    Program.SoundFontsManager.Add(new SoundFont(filename, -1, -1, -1, 0, 0, true));
                }
            }
        }
    }

    private void RefreshList()
    {
        // Reassign the list, to make sure we update the indexes
        Program.SaveConfig();
        SoundFontListView.ItemsSource = Program.SoundFontsManager.GetSoundFontList();
    }

    public void SelectedSFChanged(object? sender, SelectionChangedEventArgs e)
    {
        int index = SoundFontListView.SelectedIndex;

        if (index != -1)
        {
            var item = Program.SoundFontsManager.GetSoundFontList()[index];

            XGDrums.IsChecked = item.XGDrums;

            LinAttMod.IsChecked = item.LinAttMod;
            LinDecVol.IsChecked = item.LinDecVol;
            MinFx.IsChecked = item.MinFx;
            EnforceSBLimits.IsChecked = item.EnforceSBLimits;
            NoRampIn.IsChecked = item.NoRampIn;         

            Enabled.IsChecked = item.Enabled;

            SourceBank.Minimum = item.IsSFZ() ? 0 : -1;
            SourcePreset.Minimum = item.IsSFZ() ? 0 : -1;
            DestinationPreset.Minimum = item.IsSFZ() ? 0 : -1;

            SourceBank.Value = item.SourceBank;
            SourcePreset.Value = item.SourcePreset;
            DestinationBank.Value = item.DestinationBank;
            DestinationBankLSB.Value = item.DestinationBankLSB;
            DestinationPreset.Value = item.DestinationPreset;
            MIDIPortDestination.Value = item.MIDIPort;

            SettingsArea.IsEnabled = true;
            Enabled.IsEnabled = true;
            return;
        }

        XGDrums.IsChecked = false;
        LinAttMod.IsChecked = false;
        LinDecVol.IsChecked = false;
        MinFx.IsChecked = false;
        EnforceSBLimits.IsChecked = false;
        NoRampIn.IsChecked = false;

        Enabled.IsChecked = false;

        SourceBank.Minimum = -1;
        SourcePreset.Minimum = -1;
        DestinationPreset.Minimum = -1;

        SourceBank.Value = -1;
        SourcePreset.Value = -1;
        DestinationBank.Value = 0;
        DestinationBankLSB.Value = 0;
        DestinationPreset.Value = -1;

        SettingsArea.IsEnabled = false;
        Enabled.IsEnabled = false;
    }

    private void PresetSettingChanged(object? sender, NumericUpDownValueChangedEventArgs e)
    {
        PresetSettingChanged();
    }

    private void PresetSettingChanged(object? sender, RoutedEventArgs e)
    {
        PresetSettingChanged();
    }

    private void PresetSettingChanged()
    {
        int index = SoundFontListView.SelectedIndex;

        if (index != -1)
        {
            var item = (SoundFont)SoundFontListView.Items[index];

            item.SetPresetSettings(
                     (short)SourcePreset.Value, (short)SourceBank.Value,
                     (short)DestinationPreset.Value, (short)DestinationBank.Value, (short)DestinationBankLSB.Value,
                     (bool)Enabled.IsChecked);
        }

        RefreshList();
    }

    private void GenSettingChanged(object? sender, RoutedEventArgs e)
    {
        int index = SoundFontListView.SelectedIndex;

        if (index != -1)
        {
            var item = (SoundFont)SoundFontListView.Items[index];

            item.SetGenSettings(
                (bool)XGDrums.IsChecked,
                (bool)LinAttMod.IsChecked,
                (bool)LinDecVol.IsChecked,
                (bool)MinFx.IsChecked,
                (bool)EnforceSBLimits.IsChecked,
                (bool)NoRampIn.IsChecked);
        }

        RefreshList();
    }

    private void FileDropEnter(object? sender, DragEventArgs e)
    {
        e.DragEffects = e.Data.GetDataFormats().Contains(DataFormats.Files) ? DragDropEffects.Copy : DragDropEffects.None;
    }

    private void FileDropInit(object? sender, DragEventArgs e)
    {
        AddSFCheck(e.Data.GetFiles());
    }

    private async void AddSoundFont(object? sender, RoutedEventArgs e)
    {
        var topLevel = GetTopLevel(this);

        if (topLevel != null)
        {
            var startPath = await StorageProvider.TryGetFolderFromPathAsync(Program.Settings.LastSoundFontFolder);
            var files = await topLevel.StorageProvider.OpenFilePickerAsync(new FilePickerOpenOptions
            {
                SuggestedStartLocation = startPath,
                Title = "Import SoundFonts",
                FileTypeFilter = [SoundFont.SoundFontAll],
                AllowMultiple = true
            });

            AddSFCheck(files);
        }

        RefreshList();
    }

    private void RemoveSoundFont(object? sender, RoutedEventArgs e)
    {
        if (SoundFontListView.SelectedItems != null && SoundFontListView.SelectedItems.Count > 0)
        {
            // Let's copy the references to an array
            SoundFont[] list = new SoundFont[SoundFontListView.SelectedItems.Count];
            SoundFontListView.SelectedItems.CopyTo(list, 0);

            // Delete the items
            foreach (SoundFont sf in list)
                Program.SoundFontsManager.Remove(sf);
        }

        RefreshList();
    }

    private void MoveSoundFontUp(object? sender, RoutedEventArgs e)
    {
        if (SoundFontListView.SelectedItems != null && SoundFontListView.SelectedItems.Count > 0)
        {
            // Let's copy the references to an array
            SoundFont[] list = new SoundFont[SoundFontListView.SelectedItems.Count];
            SoundFontListView.SelectedItems.CopyTo(list, 0);

            // Move the items
            foreach (SoundFont sf in list)
                Program.SoundFontsManager.Move(sf, MoveDirection.Up);
        }

        RefreshList();
    }

    private void MoveSoundFontDown(object? sender, RoutedEventArgs e)
    {
        if (SoundFontListView.SelectedItems != null && SoundFontListView.SelectedItems.Count > 0)
        {
            // Let's copy the references to an array
            SoundFont[] list = new SoundFont[SoundFontListView.SelectedItems.Count];
            SoundFontListView.SelectedItems.CopyTo(list, 0);

            // Move the items
            foreach (SoundFont sf in list)
                Program.SoundFontsManager.Move(sf, MoveDirection.Down);
        }

        RefreshList();
    }

    private void CloseSoundFontManager(object? sender, RoutedEventArgs e)
    {
        Close();
    }
}