#include <stdio.h>
#include <stdlib.h>
#include <stdint.h>
#include <stdbool.h>
#include <string.h>
#include <math.h>
#include <windows.h>
#include <signal.h> // For signal handling
#include <limits.h> // For INT_MAX

#ifndef MAX_PATH
#define MAX_PATH 260 // Windows MAX_PATH if not defined
#endif

// --- Configuration ---
#define MAX_TRACKS 65535     // Maximum number of MIDI tracks (MIDI standard maximum)
#define AUDIO_RATE 48000     // Sample rate for output audio
#define BUFFER_SAMPLES 4096  // Size of the audio buffer (in frames) - Increased size
#define MIN_MIX_FLOATS 64    // Minimum number of *floats* (32 frames) to request from syn_mix, like original

// --- Typedefs ---
typedef uint8_t u8;
typedef uint16_t u16;
typedef uint32_t u32;
typedef uint64_t u64;
typedef int16_t s16;

typedef u64 midi_tick_t;

// --- Structs ---

// Structure to hold track state
typedef struct {
    u8* ptr;            // Current read pointer in track data
    u8* end_ptr;        // End pointer for track data
    midi_tick_t next_event_tick; // Tick time of the next event
    u32 track_id;       // Original index for tie-breaking
    u8 running_status; // Last MIDI status byte for running status
    bool active;        // Is the track still active?
} Track;

// Structure for WAV header
#pragma pack(push, 1)
typedef struct {
    // RIFF Chunk
    char riff_id[4];     // "RIFF"
    u32 riff_size;      // Size of the rest of the file
    char wave_id[4];     // "WAVE"
    // Format Chunk
    char fmt_id[4];      // "fmt "
    u32 fmt_size;       // Size of the format chunk (16)
    u16 format_tag;     // 1 for PCM
    u16 channels;       // Number of channels (2 for stereo)
    u32 sample_rate;    // Samples per second (e.g., 48000)
    u32 bytes_per_sec;  // sample_rate * block_align
    u16 block_align;    // channels * bits_per_sample / 8
    u16 bits_per_sample;// Bits per sample (16)
    // Data Chunk
    char data_id[4];     // "data"
    u32 data_size;      // Size of the raw sample data (in bytes)
} WavHeader;
#pragma pack(pop)

// --- Globals ---
// Use dynamic allocation for track arrays to support large MAX_TRACKS
static Track* g_tracks = NULL;
static Track** g_track_heap = NULL; // Min-heap for track scheduling (1-based index)
static u32 g_active_tracks = 0;
static u32 g_heap_size = 0;

// Forward declaration for new syndrv API
typedef struct _SR_Syn SR_Syn;

// Synth instances
static SR_Syn* g_synth_instance = NULL;
static SR_Syn* g_synth_instance2 = NULL; // Second synth instance for dual mode
static DWORD(__cdecl* g_syn_mix)(SR_Syn*, float*, DWORD) = NULL;
static int(__cdecl* g_syn_msgshort)(SR_Syn*, DWORD) = NULL;
static void(__cdecl* g_syn_free)(SR_Syn*) = NULL;
static void(__cdecl* g_syn_deinit)(SR_Syn*) = NULL;

// Buffers sized for stereo frames
static float g_audio_buffer[BUFFER_SAMPLES * 2]; // Stereo float buffer
static float g_audio_buffer2[BUFFER_SAMPLES * 2]; // Second buffer for dual synth mode
static s16 g_pcm_buffer[BUFFER_SAMPLES * 2];     // Stereo s16 buffer for WAV

// Configuration flags
static bool g_use_multithreading = true; // Default to multi-threading
static bool g_use_dual_synth = false;    // Default to single synth instance
static bool g_one_syn_per_channel = false; // Default to one synth for all channels
static bool g_zvnon_standard = false;    // Default to modified (non-standard) zero velocity note-on handling
static SR_Syn* g_channel_synth[16] = {NULL}; // Array of synth instances for per-channel mode

// Flag for signal handler
static volatile sig_atomic_t g_interrupted = 0;

// --- Signal Handler ---
void signal_handler(int signal) {
    if (signal == SIGINT) {
        g_interrupted = 1;
        printf("\nCtrl+C detected, attempting graceful shutdown...\n");
    }
}

// --- Utility Functions ---

// Endian swap for reading MIDI file data
u16 bswap16(u16 val) {
    return (val << 8) | (val >> 8);
}

u32 bswap32(u32 val) {
    return ((val << 24)) |
           ((val <<  8) & 0x00FF0000) |
           ((val >>  8) & 0x0000FF00) |
           ((val >> 24));
}

// Read variable-length quantity from MIDI data
u8* read_varlen(u8* ptr, u32* out_value) {
    u32 value = 0;
    u8 byte_in;
    int i;
    for (i = 0; i < 4; ++i) {
        byte_in = *ptr++;
        value = (value << 7) | (byte_in & 0x7F);
        if (!(byte_in & 0x80)) {
            break;
        }
    }
    *out_value = value;
    return ptr;
}

// --- Min-Heap Implementation ---

// Compare two tracks for heap ordering (earlier tick first, then lower track_id)
bool compare_tracks(const Track* a, const Track* b) {
    if (a->next_event_tick != b->next_event_tick) {
        return a->next_event_tick < b->next_event_tick;
    }
    return a->track_id < b->track_id;
}

void heap_swap(u32 i, u32 j) {
    Track* temp = g_track_heap[i];
    g_track_heap[i] = g_track_heap[j];
    g_track_heap[j] = temp;
}

void heap_sift_up(u32 index) {
    while (index > 1) {
        u32 parent = index / 2;
        if (compare_tracks(g_track_heap[index], g_track_heap[parent])) {
            heap_swap(index, parent);
            index = parent;
        } else {
            break;
        }
    }
}

void heap_sift_down(u32 index) {
    while (index * 2 <= g_heap_size) {
        u32 child = index * 2;
        // Select smaller child
        if (child + 1 <= g_heap_size && compare_tracks(g_track_heap[child + 1], g_track_heap[child])) {
            child++;
        }
        // Swap if child is smaller than parent
        if (compare_tracks(g_track_heap[child], g_track_heap[index])) {
            heap_swap(index, child);
            index = child;
        } else {
            break;
        }
    }
}

void heap_insert(Track* track) {
    if (g_heap_size >= MAX_TRACKS) return; // Heap full
    g_heap_size++;
    g_track_heap[g_heap_size] = track;
    heap_sift_up(g_heap_size);
}

Track* heap_extract_min() {
    if (g_heap_size == 0) return NULL;
    Track* min_track = g_track_heap[1];
    g_track_heap[1] = g_track_heap[g_heap_size];
    g_heap_size--;
    if (g_heap_size > 0) {
        heap_sift_down(1);
    }
    return min_track;
}

Track* heap_peek_min() {
     if (g_heap_size == 0) return NULL;
     return g_track_heap[1];
}

// --- WAV File Handling ---
// Takes total_frames (stereo sample pairs)
void write_wav_header(FILE* fp, u32 total_frames) {
    WavHeader header;

    memcpy(header.riff_id, "RIFF", 4);
    memcpy(header.wave_id, "WAVE", 4);
    memcpy(header.fmt_id, "fmt ", 4);
    memcpy(header.data_id, "data", 4);

    header.fmt_size = 16;
    header.format_tag = 1; // PCM
    header.channels = 2;   // Stereo
    header.sample_rate = AUDIO_RATE;
    header.bits_per_sample = 16;
    header.block_align = header.channels * header.bits_per_sample / 8; // Should be 4 for 16-bit stereo
    header.bytes_per_sec = header.sample_rate * header.block_align;
    header.data_size = total_frames * header.block_align; // Total data size in bytes
    header.riff_size = 36 + header.data_size; // 36 = size of header before data_size + data_size itself

    fseek(fp, 0, SEEK_SET);
    fwrite(&header, sizeof(WavHeader), 1, fp);
}

// --- MIDI Processing & Rendering ---

// Process a single MIDI event from a track
bool process_track_event(Track* track, u32* out_tempo_uspqn) {
    if (!track->active || track->ptr >= track->end_ptr) {
        track->active = false;
        return false; // Track finished
    }

    u8 status_byte;
    u8 byte1, byte2;

    u8 peek_byte = *track->ptr;
    if (peek_byte & 0x80) { // New status byte
        status_byte = *track->ptr++;
        track->running_status = status_byte;
    } else { // Running status
        status_byte = track->running_status;
        if (status_byte == 0) {
             fprintf(stderr, "Track %u: Error - Running status used before first status byte.\n", track->track_id);
             track->active = false;
             return false;
        }
    }

    u8 command = status_byte & 0xF0;
    // u8 channel = status_byte & 0x0F; // Unused currently
    u32 midi_message = 0;

    switch (command) {
        case 0x80: // Note Off
        case 0x90: // Note On
        case 0xA0: // Polyphonic Key Pressure (Aftertouch)
        case 0xB0: // Control Change
        case 0xE0: // Pitch Bend Change
            byte1 = *track->ptr++;
            byte2 = *track->ptr++;
            // Handle Note On with velocity 0 based on configuration
            if (command == 0x90 && byte2 == 0 && g_zvnon_standard) {
                // Standard MIDI behavior: treat as Note Off
                command = 0x80; // Treat as Note Off
                byte2 = 64; // Default velocity for Note Off
            } else if (command == 0x90) {
                // Scale down velocity for Note On messages before sending
                u8 original_velocity = byte2;
                byte2 = (u8)(original_velocity * 0.75f); // Apply scaling factor
                // Only ensure velocity is at least 1 if it wasn't originally 0
                if (byte2 < 1 && original_velocity != 0) byte2 = 1;
            }
            midi_message = (u32)status_byte | ((u32)byte1 << 8) | ((u32)byte2 << 16);

            // Route to appropriate synth instance based on channel and mode
            u8 channel = status_byte & 0x0F;
            if (g_one_syn_per_channel) {
                // Send to the channel-specific synth instance
                g_syn_msgshort(g_channel_synth[channel], midi_message);
            } else if (g_use_dual_synth && channel >= 9) {
                g_syn_msgshort(g_synth_instance2, midi_message);
            } else {
                g_syn_msgshort(g_synth_instance, midi_message);
            }
            break;

        case 0xC0: // Program Change
        case 0xD0: // Channel Pressure (Aftertouch)
            byte1 = *track->ptr++;
            midi_message = (u32)status_byte | ((u32)byte1 << 8);

            // Route to appropriate synth instance based on channel and mode
            u8 channel2 = status_byte & 0x0F;
            if (g_one_syn_per_channel) {
                // Send to the channel-specific synth instance
                g_syn_msgshort(g_channel_synth[channel2], midi_message);
            } else if (g_use_dual_synth && channel2 >= 9) {
                g_syn_msgshort(g_synth_instance2, midi_message);
            } else {
                g_syn_msgshort(g_synth_instance, midi_message);
            }
            break;

        case 0xF0: // System Exclusive (Sysex) or Meta Event
            if (status_byte == 0xFF) { // Meta Event
                u8 meta_type = *track->ptr++;
                u32 length;
                track->ptr = read_varlen(track->ptr, &length);
                if (meta_type == 0x2F) { // End of Track
                    track->active = false;
                    return false; // Signal track end
                } else if (meta_type == 0x51 && length == 3) { // Set Tempo
                    // MIDI standard: tempo is 3 bytes representing microseconds per quarter note
                    // Ensure we read it correctly as a 24-bit value
                    *out_tempo_uspqn = ((u32)track->ptr[0] << 16) | ((u32)track->ptr[1] << 8) | (u32)track->ptr[2];
                    // Validate tempo value to ensure it's reasonable
                    if (*out_tempo_uspqn == 0) {
                        fprintf(stderr, "Warning: Invalid tempo value of 0, using default 500000\n");
                        *out_tempo_uspqn = 500000; // Default to 120 BPM
                    }
                }
                // Skip other meta events
                track->ptr += length;
            } else if (status_byte == 0xF0 || status_byte == 0xF7) { // Sysex Start or Continue
                 u32 length;
                 track->ptr = read_varlen(track->ptr, &length);
                 // We don't send sysex to syndrv via short messages
                 track->ptr += length;
                 // Note: Need to handle F7 continuation if required, but syndrv likely doesn't need it via short msgs
            } else {
                 fprintf(stderr, "Track %u: Warning - Unhandled System Common message: 0x%02X\n", track->track_id, status_byte);
                 // Might need to skip based on message type if not F0/F7/FF
                 track->active = false; // Stop processing track on unknown Fx message
                 return false;
            }
            break;

        default:
            fprintf(stderr, "Track %u: Warning - Unknown MIDI command: 0x%02X\n", track->track_id, command);
            track->active = false; // Stop processing track on unknown command
            return false;
    }

    // Read delta time for the *next* event
    if (track->ptr < track->end_ptr) {
        u32 delta_ticks;
        track->ptr = read_varlen(track->ptr, &delta_ticks);
        track->next_event_tick += delta_ticks;
    } else {
        track->active = false; // No more events
    }

    return track->active;
}


// --- Main Function ---
int main(int argc, char* argv[]) {
    printf("Syndrv MIDI Renderer\n");

    // Declare track_data_buffers at the beginning to avoid uninitialized warnings
    u8** track_data_buffers = NULL;

    // --- Default Settings ---
    u32 synth_layers = 32;
    const char* output_path = NULL; // Optional output path

    // Allocate memory for tracks and heap
    g_tracks = (Track*)malloc(MAX_TRACKS * sizeof(Track));
    if (!g_tracks) {
        fprintf(stderr, "Failed to allocate memory for tracks\n");
        return 1;
    }

    // Heap is 1-based, so allocate MAX_TRACKS + 1
    g_track_heap = (Track**)malloc((MAX_TRACKS + 1) * sizeof(Track*));
    if (!g_track_heap) {
        fprintf(stderr, "Failed to allocate memory for track heap\n");
        free(g_tracks);
        return 1;
    }

    // --- Argument Parsing ---
    if (argc < 2) {
        fprintf(stderr, "Usage: %s <input.mid> [output.wav] [layers=N] [threading=multi|single] [outPath=path] [dualSyn=on|off] [zvnonStandard=normal/modified]\n", argv[0]);
        fprintf(stderr, "Defaults: layers=32, threading=multi, dualSyn=off, zvnonStandard=modified\n");
        fprintf(stderr, "If output.wav is not specified, the WAV will be created next to the MIDI with the same name\n");
        return 1;
    }

    const char* midi_filename = argv[1];
    const char* wav_filename = NULL;
    char auto_wav_filename[MAX_PATH] = {0};

    // Start parsing from argument 2 (could be output file or a flag)
    int arg_start = 2;

    // Check if second argument is a WAV file or a flag
    if (argc >= 3 && strstr(argv[2], "=") == NULL) {
        // It's not a flag, assume it's the output file
        wav_filename = argv[2];
        arg_start = 3;
    }

    // Parse optional flags
    for (int i = arg_start; i < argc; ++i) {
        if (sscanf(argv[i], "layers=%u", &synth_layers) == 1) {
            printf("Using layers: %u\n", synth_layers);
        } else if (strncmp(argv[i], "threading=", 10) == 0) {
            const char* mode = argv[i] + 10;
            if (strcmp(mode, "multi") == 0) {
                g_use_multithreading = true;
                printf("Using multi-threading mode\n");
            } else if (strcmp(mode, "single") == 0) {
                g_use_multithreading = false;
                printf("Using single-threading mode\n");
            } else {
                fprintf(stderr, "Warning: Unknown threading mode '%s', using default (multi)\n", mode);
            }
        } else if (strncmp(argv[i], "outPath=", 8) == 0) {
            output_path = argv[i] + 8;
            printf("Using output path: %s\n", output_path);
        } else if (strncmp(argv[i], "dualSyn=", 8) == 0) {
            const char* mode = argv[i] + 8;
            if (strcmp(mode, "on") == 0) {
                g_use_dual_synth = true;
                printf("Using dual synth mode (channels 0-8 and 9-16 on separate instances)\n");
            } else if (strcmp(mode, "off") == 0) {
                g_use_dual_synth = false;
                printf("Using single synth mode\n");
            } else {
                fprintf(stderr, "Warning: Unknown dualSyn mode '%s', using default (off)\n", mode);
            }
        } else if (strncmp(argv[i], "oneSynPerChan=", 14) == 0) {
            const char* mode = argv[i] + 14;
            if (strcmp(mode, "on") == 0) {
                g_one_syn_per_channel = true;
                printf("Using one synth instance per MIDI channel\n");
            } else if (strcmp(mode, "off") == 0) {
                g_one_syn_per_channel = false;
            } else {
                fprintf(stderr, "Warning: Unknown oneSynPerChan mode '%s', using default (off)\n", mode);
            }
        } else if (strncmp(argv[i], "zvnonStandard=", 14) == 0) {
            const char* mode = argv[i] + 14;
            if (strcmp(mode, "normal") == 0) {
                g_zvnon_standard = true;
                printf("Using standard MIDI handling for zero velocity note-ons (treated as note-offs)\n");
            } else if (strcmp(mode, "modified") == 0) {
                g_zvnon_standard = false;
                printf("Using modified handling for zero velocity note-ons (treated as note-ons with zero velocity)\n");
            } else {
                fprintf(stderr, "Warning: Unknown zvnonStandard mode '%s', using default (modified)\n", mode);
            }
        } else {
            fprintf(stderr, "Warning: Ignoring unknown argument '%s'\n", argv[i]);
        }
    }

    // Validate configuration
    if (g_one_syn_per_channel && g_use_multithreading) {
        fprintf(stderr, "Error: oneSynPerChan=on requires threading=single\n");
        return 1;
    }

    if (g_one_syn_per_channel && g_use_dual_synth) {
        fprintf(stderr, "Error: oneSynPerChan=on cannot be used with dualSyn=on\n");
        return 1;
    }

    // If no output file specified, generate one based on the MIDI filename
    if (wav_filename == NULL) {
        // Get the MIDI filename without extension
        const char* midi_basename = strrchr(midi_filename, '\\');
        if (midi_basename == NULL) {
            midi_basename = strrchr(midi_filename, '/');
        }
        if (midi_basename == NULL) {
            midi_basename = midi_filename;
        } else {
            midi_basename++; // Skip the slash
        }

        // Create the output path
        if (output_path != NULL) {
            // Use specified output path
            snprintf(auto_wav_filename, MAX_PATH, "%s\\%s", output_path, midi_basename);
        } else {
            // Use same directory as MIDI file
            strncpy(auto_wav_filename, midi_filename, MAX_PATH - 1);
        }

        // Replace or add .wav extension
        char* ext = strrchr(auto_wav_filename, '.');
        if (ext != NULL) {
            strcpy(ext, ".wav");
        } else {
            strncat(auto_wav_filename, ".wav", MAX_PATH - strlen(auto_wav_filename) - 1);
        }

        wav_filename = auto_wav_filename;
        printf("Auto-generated output file: %s\n", wav_filename);
    }


    // Setup signal handler
    signal(SIGINT, signal_handler);


    // --- Load syndrv ---
    // Pass parsed layers count to syn_init
    HMODULE h_syndrv = LoadLibraryA("syndrv.dll");
    if (!h_syndrv) {
        perror("Failed to load syndrv.dll");
        return 1;
    }

    SR_Syn*(__cdecl* syn_alloc)(void) = (SR_Syn*(__cdecl*)(void))GetProcAddress(h_syndrv, "syn_alloc");
    BOOL(__cdecl* syn_init)(SR_Syn*, DWORD) = (BOOL(__cdecl*)(SR_Syn*, DWORD))GetProcAddress(h_syndrv, "syn_init");
    g_syn_mix = (DWORD(__cdecl*)(SR_Syn*, float*, DWORD))GetProcAddress(h_syndrv, "syn_mix");
    g_syn_msgshort = (int(__cdecl*)(SR_Syn*, DWORD))GetProcAddress(h_syndrv, "syn_msgshort");
    g_syn_free = (void(__cdecl*)(SR_Syn*))GetProcAddress(h_syndrv, "syn_free");
    g_syn_deinit = (void(__cdecl*)(SR_Syn*))GetProcAddress(h_syndrv, "syn_deinit");

    if (!syn_alloc || !syn_init || !g_syn_mix || !g_syn_msgshort || !g_syn_free || !g_syn_deinit) {
        fprintf(stderr, "Failed to get required functions from syndrv.dll\n");
        FreeLibrary(h_syndrv);
        return 1;
    }

    // Initialize synth instances based on configuration
    if (g_one_syn_per_channel) {
        // Initialize one synth instance per MIDI channel
        printf("Initializing 16 synth instances (one per MIDI channel)...\n");
        for (int i = 0; i < 16; i++) {
            g_channel_synth[i] = syn_alloc();
            if (!g_channel_synth[i]) {
                fprintf(stderr, "syn_alloc failed for channel %d instance\n", i);
                // Clean up previously allocated instances
                for (int j = 0; j < i; j++) {
                    if (g_channel_synth[j]) {
                        g_syn_deinit(g_channel_synth[j]);
                        g_syn_free(g_channel_synth[j]);
                    }
                }
                FreeLibrary(h_syndrv);
                return 1;
            }

            if (!syn_init(g_channel_synth[i], synth_layers)) {
                fprintf(stderr, "syn_init failed for channel %d instance\n", i);
                // Clean up this and previously allocated instances
                for (int j = 0; j <= i; j++) {
                    if (g_channel_synth[j]) {
                        if (j < i) g_syn_deinit(g_channel_synth[j]);
                        g_syn_free(g_channel_synth[j]);
                        g_channel_synth[j] = NULL;
                    }
                }
                FreeLibrary(h_syndrv);
                return 1;
            }
        }
        printf("All 16 channel synth instances initialized with %u layers each.\n", synth_layers);
    } else {
        // Initialize primary synth instance
        g_synth_instance = syn_alloc();
        if (!g_synth_instance) {
            fprintf(stderr, "syn_alloc failed for primary instance\n");
            FreeLibrary(h_syndrv);
            return 1;
        }

        if (!syn_init(g_synth_instance, synth_layers)) { // Use parsed layers
            fprintf(stderr, "syn_init failed for primary instance\n");
            g_syn_free(g_synth_instance);
            g_synth_instance = NULL;
            FreeLibrary(h_syndrv);
            return 1;
        }
        printf("Primary synth instance initialized with %u layers.\n", synth_layers);

        // Initialize secondary synth instance if dual mode is enabled
        if (g_use_dual_synth) {
            g_synth_instance2 = syn_alloc();
            if (!g_synth_instance2) {
                fprintf(stderr, "syn_alloc failed for secondary instance\n");
                g_syn_deinit(g_synth_instance);
                g_syn_free(g_synth_instance);
                FreeLibrary(h_syndrv);
                return 1;
            }

            if (!syn_init(g_synth_instance2, synth_layers)) {
                fprintf(stderr, "syn_init failed for secondary instance\n");
                g_syn_free(g_synth_instance2);
                g_synth_instance2 = NULL;
                g_syn_deinit(g_synth_instance);
                g_syn_free(g_synth_instance);
                FreeLibrary(h_syndrv);
                return 1;
            }
            printf("Secondary synth instance initialized with %u layers.\n", synth_layers);
        }
    }

    printf("syndrv.dll loaded and initialized.\n");

    // --- Open MIDI file ---
    FILE* fmidi = fopen(midi_filename, "rb");
    if (!fmidi) {
        perror("Failed to open MIDI file");
        if (g_synth_instance) { // Check if synth was allocated before failing
             g_syn_deinit(g_synth_instance);
             g_syn_free(g_synth_instance);
        }
        return 1;
    }

    // --- Read MIDI Header ---
    char chunk_id[4];
    u32 chunk_size;
    u16 format, track_count, time_division;

    if (fread(chunk_id, 4, 1, fmidi) != 1) goto read_error;
    if (fread(&chunk_size, 4, 1, fmidi) != 1) goto read_error;
    chunk_size = bswap32(chunk_size);

    if (strncmp(chunk_id, "MThd", 4) != 0 || chunk_size < 6) {
        fprintf(stderr, "Invalid MIDI header chunk\n");
        fclose(fmidi);
        g_syn_deinit(g_synth_instance);
        g_syn_free(g_synth_instance);
        return 1;
    }

    if (fread(&format, 2, 1, fmidi) != 1) goto read_error;
    if (fread(&track_count, 2, 1, fmidi) != 1) goto read_error;
    if (fread(&time_division, 2, 1, fmidi) != 1) goto read_error;
    format = bswap16(format);
    track_count = bswap16(track_count);
    time_division = bswap16(time_division);

    // Skip extra header data if any
    if (chunk_size > 6) {
        if (fseek(fmidi, chunk_size - 6, SEEK_CUR) != 0) goto read_error;
    }

    printf("MIDI Format: %u, Tracks: %u, Time Division: %u (ppqn)\n", format, track_count, time_division);

    if (format > 1) {
         fprintf(stderr, "Error: Unsupported MIDI format %u (only 0 and 1 supported)\n", format);
         fclose(fmidi);
         g_syn_deinit(g_synth_instance);
         g_syn_free(g_synth_instance);
         return 1;
    }
    if (time_division & 0x8000) {
        fprintf(stderr, "Error: SMTPE time division not supported\n");
        fclose(fmidi);
        g_syn_deinit(g_synth_instance);
        g_syn_free(g_synth_instance);
        return 1;
    }

    // --- Read Tracks ---
    // Dynamically allocate track data buffer pointers
    track_data_buffers = (u8**)calloc(MAX_TRACKS, sizeof(u8*));
    if (!track_data_buffers) {
        fprintf(stderr, "Failed to allocate memory for track data buffers\n");
        if (g_synth_instance) {
            g_syn_deinit(g_synth_instance);
            g_syn_free(g_synth_instance);
        }
        free(g_tracks);
        free(g_track_heap);
        return 1;
    }
    g_active_tracks = 0; // Use global track count

    for (u32 i = 0; i < track_count; ++i) {
        // Check if we're approaching MAX_TRACKS limit
        if (g_active_tracks >= MAX_TRACKS) {
            fprintf(stderr, "Warning: Reached MAX_TRACKS limit (%u). Skipping remaining tracks.\n", MAX_TRACKS);
            // Need to skip remaining track chunks
            while (fread(chunk_id, 4, 1, fmidi) == 1) {
                 if (fread(&chunk_size, 4, 1, fmidi) != 1) break; // Error reading size
                 chunk_size = bswap32(chunk_size);
                 if (fseek(fmidi, chunk_size, SEEK_CUR) != 0) break; // Error seeking
                 if (feof(fmidi)) break;
            }
            break;
        }

        if (fread(chunk_id, 4, 1, fmidi) != 1) {
             fprintf(stderr, "Error reading track %u header id\n", i);
             break; // Error or EOF
        }
        if (fread(&chunk_size, 4, 1, fmidi) != 1) {
             fprintf(stderr, "Error reading track %u header size\n", i);
             break; // Error or EOF
        }
        chunk_size = bswap32(chunk_size);

        if (strncmp(chunk_id, "MTrk", 4) != 0) {
            fprintf(stderr, "Warning: Expected MTrk chunk, found %c%c%c%c. Skipping %u bytes.\n",
                    chunk_id[0], chunk_id[1], chunk_id[2], chunk_id[3], chunk_size);
            if (fseek(fmidi, chunk_size, SEEK_CUR) != 0) {
                 fprintf(stderr, "Error seeking past unexpected chunk.\n");
                 break;
            }
            continue;
        }

        u8* buffer = (u8*)malloc(chunk_size);
        if (!buffer) {
            perror("Failed to allocate memory for track data");
            goto cleanup_tracks; // Use goto for cleanup
        }

        if (fread(buffer, 1, chunk_size, fmidi) != chunk_size) {
            fprintf(stderr, "Error reading track %u data (%u bytes)\n", i, chunk_size);
            free(buffer); // Free the buffer we just allocated
            goto cleanup_tracks; // Use goto for cleanup
        }

        track_data_buffers[g_active_tracks] = buffer; // Store buffer pointer for later free()
        g_tracks[g_active_tracks].ptr = buffer;
        g_tracks[g_active_tracks].end_ptr = buffer + chunk_size;
        g_tracks[g_active_tracks].next_event_tick = 0;
        g_tracks[g_active_tracks].track_id = g_active_tracks; // Use the index as ID
        g_tracks[g_active_tracks].running_status = 0;
        g_tracks[g_active_tracks].active = true;

        // Read initial delta time
        u32 initial_delta;
        g_tracks[g_active_tracks].ptr = read_varlen(g_tracks[g_active_tracks].ptr, &initial_delta);
        g_tracks[g_active_tracks].next_event_tick = initial_delta;

        // Only insert if the track has events (ptr hasn't reached end_ptr just reading delta)
        if (g_tracks[g_active_tracks].ptr < g_tracks[g_active_tracks].end_ptr) {
             heap_insert(&g_tracks[g_active_tracks]);
        } else {
             g_tracks[g_active_tracks].active = false; // Mark as inactive if empty
        }
        g_active_tracks++; // Increment count regardless of insertion for buffer cleanup
    }
    fclose(fmidi); // Done reading MIDI file
    fmidi = NULL; // Avoid double close in cleanup

    printf("Read %u tracks, %u active tracks added to heap.\n", g_active_tracks, g_heap_size);

    if (g_heap_size == 0) {
        fprintf(stderr, "No active tracks with events found in MIDI file.\n");
        goto cleanup_tracks;
    }

    // --- Open WAV file ---
    FILE* fwav = fopen(wav_filename, "wb");
    if (!fwav) {
        perror("Failed to open WAV output file");
        goto cleanup_tracks;
    }

    // Write placeholder WAV header
    WavHeader placeholder_header = {0};
    fwrite(&placeholder_header, sizeof(WavHeader), 1, fwav);

// --- Rendering Loop ---
    midi_tick_t current_tick = 0;
    u64 total_frames_written = 0; // Tracks stereo frames written for WAV header
    u32 current_tempo_uspqn = 500000; // Default tempo: 120 BPM (500,000 microseconds per quarter note)
    // Use double precision timing for better accuracy
    double seconds_per_tick = (double)current_tempo_uspqn / (double)time_division / 1000000.0;
    double precise_frame_pos = 0.0; // Tracks precise frame position

    // --- Limiter State ---
    const float LIMITER_THRESHOLD = 0.98f; // Approx -0.17 dBFS
    const float ATTACK_MS = 0.1f;          // Fast attack
    const float RELEASE_MS = 100.0f;       // Moderate release
    float limiter_gain = 1.0f;             // Current gain factor
    float limiter_envelope = 0.0f;         // Current peak envelope
    // Calculate coefficients based on sample rate
    float attack_coeff = expf(-1.0f / (ATTACK_MS * AUDIO_RATE * 0.001f));
    float release_coeff = expf(-1.0f / (RELEASE_MS * AUDIO_RATE * 0.001f));


    printf("Rendering...\n");
    while (g_heap_size > 0 && !g_interrupted) { // Check interrupt flag here
        Track* next_track = heap_peek_min();
        midi_tick_t next_event_tick = next_track->next_event_tick;

        // Calculate time to render until the next event
        midi_tick_t ticks_to_render = next_event_tick - current_tick;
        if (ticks_to_render > 0) {
            // Calculate time to render with high precision
            double seconds_to_render = (double)ticks_to_render * (double)seconds_per_tick;
            // Target frame position based on precise double calculation
            double target_precise_frame = (double)precise_frame_pos + seconds_to_render * (double)AUDIO_RATE;
            // Calculate frames needed to reach the target frame position
            u64 target_frame_count = (u64)target_precise_frame; // Target total frames

            // Ensure frames_to_generate doesn't underflow
            u32 frames_to_generate = (target_frame_count > total_frames_written) ? (u32)(target_frame_count - total_frames_written) : 0;

            // Generate audio in chunks
            while (frames_to_generate > 0 && !g_interrupted) { // Check interrupt flag here too
                // Determine number of frames to request in this chunk
                u32 frames_this_chunk = frames_to_generate > BUFFER_SAMPLES ? BUFFER_SAMPLES : frames_to_generate;

                // Call syn_mix with frame count and the multithreading flag if enabled
                u32 mix_flags = frames_this_chunk;
                if (g_use_multithreading) {
                    mix_flags |= (1U << 31); // Set multithreading flag
                }

                int frames_mixed = 0;

                if (g_one_syn_per_channel) {
                    // Mix from all 16 channel synth instances
                    memset(g_audio_buffer, 0, sizeof(g_audio_buffer)); // Clear buffer
                    int min_frames = INT_MAX;
                    bool any_frames = false;

                    // Temporary buffer for each channel
                    float channel_buffer[BUFFER_SAMPLES * 2];

                    for (int ch = 0; ch < 16; ch++) {
                        memset(channel_buffer, 0, sizeof(channel_buffer));
                        DWORD ch_frames = g_syn_mix(g_channel_synth[ch], channel_buffer, mix_flags);
                        ch_frames &= 0xFFFFFF; // Mask out upper bits

                        if (ch_frames > 0) {
                            any_frames = true;
                            if (ch_frames < min_frames) {
                                min_frames = ch_frames;
                            }

                            // Mix this channel's audio into the main buffer
                            for (int i = 0; i < ch_frames * 2; i++) {
                                g_audio_buffer[i] += channel_buffer[i] * 0.0625f; // Scale by 1/16
                            }
                        }
                    }

                    if (!any_frames || min_frames <= 0) {
                        fprintf(stderr, "Warning: No audio generated from channel synths, stopping render.\n");
                        goto render_end;
                    }

                    frames_mixed = min_frames;
                } else if (g_use_dual_synth) {
                    // Mix from both synth instances
                    DWORD frames_mixed1 = g_syn_mix(g_synth_instance, g_audio_buffer, mix_flags);
                    DWORD frames_mixed2 = g_syn_mix(g_synth_instance2, g_audio_buffer2, mix_flags);

                    // Mask out upper bits just in case
                    frames_mixed1 &= 0xFFFFFF;
                    frames_mixed2 &= 0xFFFFFF;

                    // Use the smaller of the two frame counts
                    frames_mixed = (frames_mixed1 < frames_mixed2) ? frames_mixed1 : frames_mixed2;

                    if (frames_mixed <= 0) {
                        fprintf(stderr, "Warning: syn_mix returned %d and %d, stopping render.\n", frames_mixed1, frames_mixed2);
                        goto render_end; // Exit loop if synth stops producing audio
                    }

                    // Mix the two buffers together
                    for (int i = 0; i < frames_mixed * 2; i++) {
                        g_audio_buffer[i] = (g_audio_buffer[i] + g_audio_buffer2[i]) * 0.5f; // Average the samples
                    }
                } else {
                    // Single synth instance
                    frames_mixed = (int)g_syn_mix(g_synth_instance, g_audio_buffer, mix_flags);

                    // Mask out upper bits just in case
                    frames_mixed &= 0xFFFFFF;

                    if (frames_mixed <= 0) {
                        fprintf(stderr, "Warning: syn_mix returned %d, stopping render.\n", frames_mixed);
                        goto render_end; // Exit loop if synth stops producing audio
                    }
                }

                // --- Process Samples (Limiter + Conversion) ---
                int samples_in_buffer = frames_mixed * 2; // Total float samples in this chunk
                for (int i = 0; i < samples_in_buffer; i++) {
                    float sample_in = g_audio_buffer[i];
                    float abs_sample = fabsf(sample_in);

                    // Update envelope - use faster attack, slower release
                    if (abs_sample > limiter_envelope) {
                        limiter_envelope = attack_coeff * limiter_envelope + (1.0f - attack_coeff) * abs_sample;
                    } else {
                        limiter_envelope = release_coeff * limiter_envelope + (1.0f - release_coeff) * abs_sample;
                        // Ensure envelope doesn't decay below smallest representable float if needed, but likely fine
                    }

                    // Calculate target gain if envelope exceeds threshold
                    float target_gain = 1.0f;
                    if (limiter_envelope > LIMITER_THRESHOLD) {
                        target_gain = LIMITER_THRESHOLD / limiter_envelope;
                    }

                    // Smooth gain - apply same attack/release smoothing to gain changes
                    if (target_gain < limiter_gain) { // Gain reduction needed (attack)
                         limiter_gain = attack_coeff * limiter_gain + (1.0f - attack_coeff) * target_gain;
                    } else { // Gain recovery (release)
                         limiter_gain = release_coeff * limiter_gain + (1.0f - release_coeff) * target_gain;
                    }

                    // Apply limiter gain
                    float limited_sample = sample_in * limiter_gain;

                    // Convert to s16 and clamp (apply final hard clip just in case)
                    float scaled_sample = limited_sample * 32767.0f;
                    if (scaled_sample > 32767.0f) g_pcm_buffer[i] = 32767;
                    else if (scaled_sample < -32768.0f) g_pcm_buffer[i] = -32768; // Correct s16 min
                    else g_pcm_buffer[i] = (s16)scaled_sample;
                }
                 // --- End Sample Processing ---

                 // Write the total number of s16 samples processed (frames_mixed * 2)
                fwrite(g_pcm_buffer, sizeof(s16), frames_mixed * 2, fwav);

                 // Accumulate frames written
                total_frames_written += frames_mixed;
                frames_to_generate -= frames_mixed; // Decrement frames remaining
            }
             // Update precise frame position based on seconds rendered
             // Use the actual frames written for more accuracy
             precise_frame_pos = (double)total_frames_written;
        }

        // Check for interrupt signal before processing next event batch
        if (g_interrupted) {
            printf("Interrupt detected during rendering gap.\n");
            goto render_end;
        }

        // Process all events at the current tick
        current_tick = next_event_tick;
        while (g_heap_size > 0 && heap_peek_min()->next_event_tick == current_tick && !g_interrupted) { // Check interrupt flag
             // Check for interrupt before processing each event within the same tick
            if (g_interrupted) {
                 printf("Interrupt detected during event processing.\n");
                 goto render_end;
            }

            Track* track_to_process = heap_extract_min();
            u32 old_tempo = current_tempo_uspqn;

            if (process_track_event(track_to_process, &current_tempo_uspqn)) {
                // Re-insert track into heap if it's still active
                heap_insert(track_to_process);
            } else {
                // Track finished or encountered an error
                // Buffer is freed later
            }

            // Update timing if tempo changed
            if (current_tempo_uspqn != old_tempo) {
                // Calculate new seconds_per_tick based on new tempo with double precision
                seconds_per_tick = (double)current_tempo_uspqn / (double)time_division / 1000000.0;

                // Log tempo change for debugging
                printf("Tempo change: %u us/qn (%.2f BPM)\n",
                       current_tempo_uspqn,
                       60000000.0 / (double)current_tempo_uspqn);
            }
        }
         // Progress indicator (using ticks)
        if (!g_interrupted && (current_tick % (time_division * 4)) == 0) { // Update roughly every measure (assuming 4/4)
             printf("Tick: %lu \r", (unsigned long)current_tick);
             fflush(stdout);
        }
    }

render_end:
    printf("\nRendering finished. Total frames: %lu\n", (unsigned long)total_frames_written); // Changed label to frames

    // --- Finalize WAV ---
    if (fwav) { // Check if file was opened successfully
         write_wav_header(fwav, (u32)total_frames_written); // Pass total frames
         fclose(fwav);
         printf("WAV file '%s' written.\n", wav_filename);
    } else {
         printf("WAV file '%s' could not be opened or written.\n", wav_filename);
    }


cleanup_tracks: // Label for cleaning up track buffers
    if (fmidi) fclose(fmidi); // Close MIDI file if it was opened

    // --- Cleanup ---
    if (g_one_syn_per_channel) {
        // Clean up all channel synth instances
        for (int i = 0; i < 16; i++) {
            if (g_channel_synth[i]) {
                g_syn_deinit(g_channel_synth[i]);
                g_syn_free(g_channel_synth[i]);
                g_channel_synth[i] = NULL;
            }
        }
    } else {
        // Clean up main synth instances
        if (g_synth_instance) {
            g_syn_deinit(g_synth_instance);
            g_syn_free(g_synth_instance);
        }

        if (g_synth_instance2) {
            g_syn_deinit(g_synth_instance2);
            g_syn_free(g_synth_instance2);
        }
    }
    for (u32 i = 0; i < g_active_tracks; ++i) { // Use g_active_tracks for loop limit
        if (track_data_buffers[i]) {
            free(track_data_buffers[i]);
        }
    }
    free(track_data_buffers); // Free the array of pointers

    // Free dynamically allocated arrays
    if (g_tracks) {
        free(g_tracks);
        g_tracks = NULL;
    }

    if (g_track_heap) {
        free(g_track_heap);
        g_track_heap = NULL;
    }

    printf("Cleanup complete.\n");
    return 0;

read_error: // Label for handling read errors cleanly
    fprintf(stderr, "Error reading MIDI file.\n");
    if (fmidi) fclose(fmidi);
    if (g_one_syn_per_channel) {
        // Clean up all channel synth instances
        for (int i = 0; i < 16; i++) {
            if (g_channel_synth[i]) {
                g_syn_deinit(g_channel_synth[i]);
                g_syn_free(g_channel_synth[i]);
            }
        }
    } else {
        if (g_synth_instance) {
            g_syn_deinit(g_synth_instance);
            g_syn_free(g_synth_instance);
        }

        if (g_synth_instance2) {
            g_syn_deinit(g_synth_instance2);
            g_syn_free(g_synth_instance2);
        }
    }

    // track_data_buffers might not be initialized in this error path

    // Free any track buffers allocated so far
    if (track_data_buffers) {
        for (u32 i = 0; i < g_active_tracks; ++i) {
            if (track_data_buffers[i]) {
                free(track_data_buffers[i]);
            }
        }
        free(track_data_buffers); // Free the array of pointers
    }
    // Free dynamically allocated arrays
    if (g_tracks) {
        free(g_tracks);
        g_tracks = NULL;
    }

    if (g_track_heap) {
        free(g_track_heap);
        g_track_heap = NULL;
    }

    return 1;
}
