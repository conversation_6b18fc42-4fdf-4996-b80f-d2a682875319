<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:ctrl="using:GroupBox.Avalonia.Controls"
        xmlns:themes="using:GroupBox.Avalonia.Themes"
		xmlns:oc="using:OmniConverter"
        mc:Ignorable="d" d:DesignWidth="885" d:DesignHeight="350"
		Width="885" Height="350"
        x:Class="OmniConverter.InfoWindow"
        Title="Information"
	    WindowStartupLocation="CenterScreen"
		CanResize="False"
        RequestedThemeVariant="Dark">
	<Grid ColumnDefinitions="325, 1*" Margin="10" >
		<Image Grid.Column="0" Source="/Assets/OmniConverter.png" RenderOptions.BitmapInterpolationMode="HighQuality" />
		<Grid Grid.Column="1" RowDefinitions="1*, 1*">
			<StackPanel Grid.Row="0" Margin="10, 0, 0, 0">
				<Grid ColumnDefinitions="8*, 1*" Margin="0, 0, 0, 0">
					<Grid Grid.Column="0">
						<Label Name="ConvBrand" FontSize="26">OmniConverter (pl) PR</Label>
					</Grid>
					<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Grid.Column="1">
						<Image Width="32" Height="32" HorizontalAlignment="Right" Source="/Assets/License.png" Cursor="Hand" PointerPressed="LicensePage"/>
						<Image Width="32" Height="32" HorizontalAlignment="Right" Source="/Assets/Octocat.png" Cursor="Hand" PointerPressed="GitHubPage" />
					</StackPanel>
				</Grid>
				<Separator />
				<TextBlock Grid.Row="0" Name="CopyrightText">
					EMPTY
				</TextBlock>
			</StackPanel>
			<Grid Grid.Row="1" RowDefinitions="1*, 32">
				<ctrl:GroupBox Grid.Row="0" Margin="10, 0, 0, 10" Header="Renderer information" Theme="{StaticResource GroupBoxClassic}" VerticalAlignment="Bottom" >
					<StackPanel>
						<Grid ColumnDefinitions="150, 6*">
							<Label Grid.Column="0">BASS version:</Label>
							<Label Grid.Column="1" Name="BASSVersion">-</Label>
						</Grid>
						<Grid ColumnDefinitions="150, 6*">
							<Label Grid.Column="0">BASSMIDI version:</Label>
							<Label Grid.Column="1" Name="BMIDIVersion">-</Label>
						</Grid>
						<Grid ColumnDefinitions="150, 6*">
							<Label Grid.Column="0">XSynth version:</Label>
							<Label Grid.Column="1" Name="XSynthVersion">-</Label>
						</Grid>
						<Grid ColumnDefinitions="150, 6*">
							<Label Grid.Column="0">Update branch:</Label>
							<Label Grid.Column="1" Name="CurUpdateBranch">-</Label>
						</Grid>
					</StackPanel>
				</ctrl:GroupBox>
				<Grid Grid.Row="1" ColumnDefinitions="1*, 2*" Margin="10, 0, 0, 0">
					<Grid Grid.Column="0">
						<Button Content="Change branch" HorizontalContentAlignment="Center" Click="ChangeBranch" />
					</Grid>
					<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Grid.Column="1">
						<Button Content="Check for updates" HorizontalContentAlignment="Center" Width="160" Margin="0, 0, 4, 0" Click="CheckForUpdates" />
						<Button Content="OK" HorizontalContentAlignment="Center" Width="96" Click="CloseWindow" />
					</StackPanel>
				</Grid>
			</Grid>
		</Grid>
	</Grid>
</Window>
