#include <stdio.h>
#include <windows.h>

// Forward declaration for new syndrv API
typedef struct _SR_Syn SR_Syn;

int main() {
    printf("Testing syndrv.dll loading...\n");
    
    HMODULE h_syndrv = LoadLibraryA("syndrv.dll");
    if (!h_syndrv) {
        printf("Failed to load syndrv.dll\n");
        return 1;
    }
    
    printf("syndrv.dll loaded successfully\n");
    
    // Test function loading
    SR_Syn*(__cdecl* syn_alloc)(void) = (SR_Syn*(__cdecl*)(void))GetProcAddress(h_syndrv, "syn_alloc");
    BOOL(__cdecl* syn_init)(SR_Syn*, DWORD) = (BOOL(__cdecl*)(SR_Syn*, DWORD))GetProcAddress(h_syndrv, "syn_init");
    DWORD(__cdecl* syn_mix)(SR_Syn*, float*, DWORD) = (DWORD(__cdecl*)(SR_Syn*, float*, DWORD))GetProcAddress(h_syndrv, "syn_mix");
    int(__cdecl* syn_msgshort)(SR_Syn*, DWORD) = (int(__cdecl*)(SR_Syn*, DWORD))GetProcAddress(h_syndrv, "syn_msgshort");
    void(__cdecl* syn_free)(SR_Syn*) = (void(__cdecl*)(SR_Syn*))GetProcAddress(h_syndrv, "syn_free");
    void(__cdecl* syn_deinit)(SR_Syn*) = (void(__cdecl*)(SR_Syn*))GetProcAddress(h_syndrv, "syn_deinit");
    
    if (!syn_alloc || !syn_init || !syn_mix || !syn_msgshort || !syn_free || !syn_deinit) {
        printf("Failed to get required functions from syndrv.dll\n");
        FreeLibrary(h_syndrv);
        return 1;
    }
    
    printf("All functions loaded successfully\n");
    
    // Test synth allocation
    SR_Syn* synth = syn_alloc();
    if (!synth) {
        printf("Failed to allocate synth instance\n");
        FreeLibrary(h_syndrv);
        return 1;
    }
    
    printf("Synth instance allocated successfully\n");
    
    // Test synth initialization
    if (!syn_init(synth, 32)) {
        printf("Failed to initialize synth instance\n");
        syn_free(synth);
        FreeLibrary(h_syndrv);
        return 1;
    }
    
    printf("Synth instance initialized successfully\n");
    
    // Test a simple mix call
    float buffer[128] = {0}; // 64 stereo frames
    DWORD frames = syn_mix(synth, buffer, 64);
    printf("syn_mix returned %u frames\n", frames);
    
    // Cleanup
    syn_deinit(synth);
    syn_free(synth);
    FreeLibrary(h_syndrv);
    
    printf("Test completed successfully!\n");
    return 0;
}
