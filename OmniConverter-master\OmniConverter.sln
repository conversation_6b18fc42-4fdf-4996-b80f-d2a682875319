﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.9.34728.123
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "OmniConverter", "OmniConverter\OmniConverter.csproj", "{B8123F7B-5CDA-48AD-804B-ACF334188F11}"
	ProjectSection(ProjectDependencies) = postProject
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB} = {1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}
		{5147DAD3-EB9D-466E-B787-5867CF977377} = {5147DAD3-EB9D-466E-B787-5867CF977377}
		{A622A3B1-C497-4B14-A61D-BD115CD82769} = {A622A3B1-C497-4B14-A61D-BD115CD82769}
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A} = {EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}
	EndProjectSection
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MIDIModificationFramework", "MIDIModificationFramework\MIDIModificationFramework.csproj", "{A5EBE366-17DF-44A9-A870-FB274EA299AE}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Bass", "ManagedBass\src\Bass\Portable\Bass.csproj", "{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BassMidi", "ManagedBass\src\AddOns\BassMidi\Portable\BassMidi.csproj", "{A622A3B1-C497-4B14-A61D-BD115CD82769}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "BassFx", "ManagedBass\src\AddOns\BassFx\BassFx.csproj", "{5147DAD3-EB9D-466E-B787-5867CF977377}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Monad.FLParser", "FLParser\Monad.FLParser.csproj", "{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{8FC8E176-AC27-4A80-ABB7-56DF32303768}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "NFluidsynth", "nfluidsynth\NFluidsynth\NFluidsynth.csproj", "{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|ARM64 = Debug|ARM64
		Debug|x64 = Debug|x64
		Release|Any CPU = Release|Any CPU
		Release|ARM64 = Release|ARM64
		Release|x64 = Release|x64
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Debug|ARM64.ActiveCfg = Debug|ARM64
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Debug|ARM64.Build.0 = Debug|ARM64
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Debug|x64.ActiveCfg = Debug|x64
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Debug|x64.Build.0 = Debug|x64
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Release|Any CPU.Build.0 = Release|Any CPU
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Release|ARM64.ActiveCfg = Release|ARM64
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Release|ARM64.Build.0 = Release|ARM64
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Release|x64.ActiveCfg = Release|x64
		{B8123F7B-5CDA-48AD-804B-ACF334188F11}.Release|x64.Build.0 = Release|x64
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Debug|ARM64.Build.0 = Debug|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Debug|x64.Build.0 = Debug|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Release|ARM64.ActiveCfg = Release|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Release|ARM64.Build.0 = Release|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Release|x64.ActiveCfg = Release|Any CPU
		{A5EBE366-17DF-44A9-A870-FB274EA299AE}.Release|x64.Build.0 = Release|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Debug|ARM64.Build.0 = Debug|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Debug|x64.ActiveCfg = Debug|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Debug|x64.Build.0 = Debug|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Release|Any CPU.Build.0 = Release|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Release|ARM64.ActiveCfg = Release|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Release|ARM64.Build.0 = Release|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Release|x64.ActiveCfg = Release|Any CPU
		{EAC480C1-E85D-40FB-8B23-9A1D7F050C2A}.Release|x64.Build.0 = Release|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Debug|ARM64.Build.0 = Debug|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Debug|x64.ActiveCfg = Debug|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Debug|x64.Build.0 = Debug|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Release|Any CPU.Build.0 = Release|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Release|ARM64.ActiveCfg = Release|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Release|ARM64.Build.0 = Release|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Release|x64.ActiveCfg = Release|Any CPU
		{A622A3B1-C497-4B14-A61D-BD115CD82769}.Release|x64.Build.0 = Release|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Debug|ARM64.Build.0 = Debug|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Debug|x64.ActiveCfg = Debug|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Debug|x64.Build.0 = Debug|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Release|Any CPU.Build.0 = Release|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Release|ARM64.ActiveCfg = Release|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Release|ARM64.Build.0 = Release|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Release|x64.ActiveCfg = Release|Any CPU
		{5147DAD3-EB9D-466E-B787-5867CF977377}.Release|x64.Build.0 = Release|Any CPU
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Debug|ARM64.Build.0 = Debug|Any CPU
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Debug|x64.ActiveCfg = Debug|x64
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Debug|x64.Build.0 = Debug|x64
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Release|Any CPU.Build.0 = Release|Any CPU
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Release|ARM64.ActiveCfg = Release|Any CPU
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Release|ARM64.Build.0 = Release|Any CPU
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Release|x64.ActiveCfg = Release|x64
		{4783C7B9-31B4-4BBB-9AC2-0EADB1139528}.Release|x64.Build.0 = Release|x64
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Debug|ARM64.ActiveCfg = Debug|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Debug|ARM64.Build.0 = Debug|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Debug|x64.ActiveCfg = Debug|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Debug|x64.Build.0 = Debug|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Release|Any CPU.Build.0 = Release|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Release|ARM64.ActiveCfg = Release|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Release|ARM64.Build.0 = Release|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Release|x64.ActiveCfg = Release|Any CPU
		{1BCEB4D4-E062-4A84-8810-9CF63B9AF0DB}.Release|x64.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {ACAEBAB4-94AE-4817-A03F-D5E6FBBDC676}
	EndGlobalSection
	GlobalSection(SharedMSBuildProjectFiles) = preSolution
		ManagedBass\src\AddOns\BassMidi\Shared\Shared.projitems*{a622a3b1-c497-4b14-a61d-bd115cd82769}*SharedItemsImports = 5
		ManagedBass\src\Bass\Shared\Shared.projitems*{eac480c1-e85d-40fb-8b23-9a1d7f050c2a}*SharedItemsImports = 5
	EndGlobalSection
EndGlobal
