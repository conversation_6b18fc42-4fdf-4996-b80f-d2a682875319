#include <stdio.h>
#include <windows.h>

int main() {
    printf("Testing syndrv.dll loading with old API...\n");
    
    HMODULE h_syndrv = LoadLibraryA("syndrv.dll");
    if (!h_syndrv) {
        printf("Failed to load syndrv.dll\n");
        return 1;
    }
    
    printf("syndrv.dll loaded successfully\n");
    
    // Test function loading with old API
    LPVOID(__cdecl* syn_alloc)(void) = (LPVOID)GetProcAddress(h_syndrv, "syn_alloc");
    BOOL(__cdecl* syn_init)(LPVOID, DWORD) = (LPVOID)GetProcAddress(h_syndrv, "syn_init");
    int(__cdecl* syn_mix)(LPVOID, LPVOID, DWORD) = (int(__cdecl*)(LPVOID, LPVOID, DWORD))GetProcAddress(h_syndrv, "syn_mix");
    int(__cdecl* syn_msgshort)(LPVOID, DWORD) = (int(__cdecl*)(LPVOID, DWORD))GetProcAddress(h_syndrv, "syn_msgshort");
    void(__cdecl* syn_free)(LPVOID) = (void(__cdecl*)(LPVOID))GetProcAddress(h_syndrv, "syn_free");
    void(__cdecl* syn_deinit)(LPVOID) = (void(__cdecl*)(LPVOID))GetProcAddress(h_syndrv, "syn_deinit");
    
    if (!syn_alloc || !syn_init || !syn_mix || !syn_msgshort || !syn_free) {
        printf("Failed to get required functions from syndrv.dll\n");
        printf("syn_alloc: %p\n", syn_alloc);
        printf("syn_init: %p\n", syn_init);
        printf("syn_mix: %p\n", syn_mix);
        printf("syn_msgshort: %p\n", syn_msgshort);
        printf("syn_free: %p\n", syn_free);
        printf("syn_deinit: %p\n", syn_deinit);
        FreeLibrary(h_syndrv);
        return 1;
    }
    
    printf("All functions loaded successfully\n");
    
    // Test synth allocation
    LPVOID synth = syn_alloc();
    if (!synth) {
        printf("Failed to allocate synth instance\n");
        FreeLibrary(h_syndrv);
        return 1;
    }
    
    printf("Synth instance allocated successfully\n");
    
    // Test synth initialization
    if (!syn_init(synth, 32)) {
        printf("Failed to initialize synth instance\n");
        syn_free(synth);
        FreeLibrary(h_syndrv);
        return 1;
    }
    
    printf("Synth instance initialized successfully\n");
    
    // Test a simple mix call
    float buffer[128] = {0}; // 64 stereo frames
    int frames = syn_mix(synth, buffer, 64);
    printf("syn_mix returned %d frames\n", frames);
    
    // Cleanup
    if (syn_deinit) {
        syn_deinit(synth);
    }
    syn_free(synth);
    FreeLibrary(h_syndrv);
    
    printf("Test completed successfully!\n");
    return 0;
}
