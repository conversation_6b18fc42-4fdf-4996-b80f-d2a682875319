<Window xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
		xmlns:ctrl="using:GroupBox.Avalonia.Controls"
        xmlns:themes="using:GroupBox.Avalonia.Themes"
		xmlns:oc="using:OmniConverter"
        mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="650"
		Width="800" Height="650"
        x:Class="OmniConverter.SettingsWindow"
        Title="Settings"
		ShowInTaskbar="False"
		CanResize="False"
		WindowStartupLocation="CenterOwner"
		RequestedThemeVariant="Dark">
	<Grid RowDefinitions="1*, 80">
		<TabControl Grid.Row="0">
			<TabItem Header="Synthesizer">
				<ScrollViewer VerticalScrollBarVisibility="Visible">
					<StackPanel Margin="4">
						<Grid Name="RendererPanel" Margin="2"  ColumnDefinitions="2*, 1*">
							<Label Grid.Column="0" Content="Synthesizer" VerticalAlignment="Center"/>
							<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Grid.Column="1">
								<ComboBox Name="SelectedRenderer" Width="180" SelectedIndex="0" MaxDropDownHeight="200" SelectionChanged="AudioRendererChanged" >
									<ComboBoxItem>null</ComboBoxItem>
								</ComboBox>
							</StackPanel>
						</Grid>
						<ctrl:GroupBox Header="Global Settings" Theme="{StaticResource GroupBoxClassic}" Margin="0, 16, 0, 0" >
							<StackPanel>
								<Grid Name="MaxVoicesGrid" ColumnDefinitions="1*, 2*" Margin="2">
									<Label Grid.Column="0" Content="Voice limit" VerticalAlignment="Center"/>
									<NumericUpDown Name="MaxVoices" HorizontalAlignment="Right" Width="180" FormatString="0" Value="100000" Minimum="1" Maximum="14000000" ValueChanged="MaxVoicesChanged" Grid.Column="1" />
								</Grid>
								<Grid ColumnDefinitions="4*, 1*" Margin="2">
									<Label Grid.Column="0" Content="Sample rate" VerticalAlignment="Center"/>
									<ComboBox Name="SampleRate" HorizontalAlignment="Right" Grid.Column="1" Width="180" SelectedIndex="5" MaxDropDownHeight="200">
										<ComboBoxItem>384000</ComboBoxItem>
										<ComboBoxItem>192000</ComboBoxItem>
										<ComboBoxItem>96000</ComboBoxItem>
										<ComboBoxItem>88200</ComboBoxItem>
										<ComboBoxItem>64000</ComboBoxItem>
										<ComboBoxItem>48000</ComboBoxItem>
										<ComboBoxItem>44100</ComboBoxItem>
										<ComboBoxItem>32000</ComboBoxItem>
										<ComboBoxItem>22050</ComboBoxItem>
										<ComboBoxItem>11025</ComboBoxItem>
										<ComboBoxItem>8000</ComboBoxItem>
										<ComboBoxItem>4000</ComboBoxItem>
									</ComboBox>
								</Grid>
								<Grid Name="InterpolationPanel" Margin="2" ColumnDefinitions="2*, 1*">
									<Label Content="Interpolation mode" VerticalAlignment="Center"/>
									<ComboBox Name="InterpolationSelection" HorizontalAlignment="Right" Grid.Column="1" Width="180" SelectedIndex="0" MaxDropDownHeight="200" >
										<ComboBoxItem>None</ComboBoxItem>
										<ComboBoxItem>Linear inter.</ComboBoxItem>
										<ComboBoxItem>8 point sinc inter.</ComboBoxItem>
										<ComboBoxItem>16 point sinc inter.</ComboBoxItem>
										<ComboBoxItem>32 point sinc inter.</ComboBoxItem>
										<ComboBoxItem>64 point sinc inter.</ComboBoxItem>
									</ComboBox>
								</Grid>
								<CheckBox Name="KilledNoteFading" Content="Disable sample fade out when killing a note" Margin="2" />
								<CheckBox Name="DisableFX" Content="Disable sound FXs (Reverb, Chorus etc.)" Margin="2" />
							</StackPanel>
						</ctrl:GroupBox>
						<ctrl:GroupBox Name="BASSSettingsPanel" Header="BASS Settings" Theme="{StaticResource GroupBoxClassic}" Margin="0, 16, 0, 0" >
							<StackPanel>
								<CheckBox Name="BASS_NoteOff1" Content="Follow overlaps when processing note off events" Margin="2" />
							</StackPanel>
						</ctrl:GroupBox>
						<ctrl:GroupBox Name="XSynthSettingsPanel" Header="XSynth Settings" Theme="{StaticResource GroupBoxClassic}" Margin="0, 16, 0, 0" >
							<StackPanel>
								<Grid ColumnDefinitions="1*, 2*" Margin="2">
									<Label Grid.Column="0" Content="Layer limit" VerticalAlignment="Center"/>
									<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Grid.Column="1" >
										<Label Content="&#x2753;" ToolTip.Tip="One layer is one voice per key per channel" Margin="0, 0, 10, 0" />
										<CheckBox Name="XSynth_NoLayerLimit" Content="Unlimited" Margin="0, 0, 10, 0" IsCheckedChanged="UnlimitedLayersCheck"/>
										<NumericUpDown Name="XSynth_MaxLayers" HorizontalAlignment="Right" Width="180" FormatString="0" Value="32" Minimum="1" Maximum="10000000" />
									</StackPanel>
								</Grid>
								<Grid Margin="2" ColumnDefinitions="2*, 1*">
									<Label Content="Multithreading mode" VerticalAlignment="Center"/>
									<ComboBox Name="XSynth_ThreadingSelection" HorizontalAlignment="Right" Grid.Column="1" Width="180" SelectedIndex="2" MaxDropDownHeight="200" >
										<ComboBoxItem>None</ComboBoxItem>
										<ComboBoxItem>Per channel</ComboBoxItem>
										<ComboBoxItem>Per key</ComboBoxItem>
									</ComboBox>
								</Grid>
								<CheckBox Name="XSynth_UseEffects" Content="Use sound effects (Cutoff etc.)" Margin="2" />
								<CheckBox Name="XSynth_LinearEnv" Content="Use linear decay and release phases in volume envelopes" Margin="2" />
							</StackPanel>
						</ctrl:GroupBox>
						<ctrl:GroupBox Header="Real Time Playback Simulation" Theme="{StaticResource GroupBoxClassic}" Margin="0, 16, 0, 0" >
							<Grid ColumnDefinitions="1*, 1*" >
								<CheckBox Name="RTSMode" Grid.Column="0" Content="Enable real time playback simulation" IsCheckedChanged="RTSModeCheck" />
								<StackPanel Name="RTSPanel" Grid.Column="1" Orientation="Vertical">
									<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="2">
										<Label Grid.Column="0" Content="FPS" VerticalAlignment="Center"/>
										<NumericUpDown Name="RTSFPS" HorizontalAlignment="Right" Grid.Column="2" Width="160" Minimum="0" Maximum="9999" FormatString="0.00" />
									</StackPanel>
									<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="2">
										<Label Content="Instability (%)" VerticalAlignment="Center"/>
										<NumericUpDown Name="RTSFluct" HorizontalAlignment="Right" Width="160" Minimum="0" Maximum="100" FormatString="0.00" />
									</StackPanel>
								</StackPanel>
							</Grid>
						</ctrl:GroupBox>
					</StackPanel>
				</ScrollViewer>
			</TabItem>
			<TabItem Header="Events processing">
				<StackPanel Margin="4">
					<Grid Name="NoteForceEndDelayGrid" ColumnDefinitions="1*, 1.2*" Margin="2">
						<Label Grid.Column="0" Content="Max sustain time after the last event (ms)" VerticalAlignment="Center" />
						<NumericUpDown Name="NoteForceEndDelay" HorizontalAlignment="Right" Width="180" FormatString="0" Value="3000" Minimum="0" Grid.Column="1" />
					</Grid>
					<StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
						<CheckBox Name="ZeroVelocityNoteOns" Content="Do not treat zero velocity Note On events as Note Offs" Margin="2, 16, 0, 0"/>
						<Image Name="ZeroVelocityNoteOnsWng" Width="24" Source="/Assets/Warning.png" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="8, 16, 0, 0" IsVisible="True" PointerPressed="ZeroVelocityNoteOnsMsg" />
					</StackPanel>
					<Grid ColumnDefinitions="1*, 1.2*" Margin="2, 16, 0, 0">
						<CheckBox Name="FilterVelocity" Content="Ignore notes with velocity in this range" IsCheckedChanged="FilterVelocityCheck" Grid.Column="0"/>
						<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Grid.Column="1" >
							<Label Content="Low" VerticalAlignment="Center"/>
							<NumericUpDown Name="VelocityLowValue" HorizontalAlignment="Right" VerticalAlignment="Center" Width="128" Minimum="1" Maximum="127" FormatString="0" Value="1" Margin="0, 0, 8, 0"/>
							<Label Content="High" VerticalAlignment="Center"/>
							<NumericUpDown Name="VelocityHighValue" HorizontalAlignment="Right" VerticalAlignment="Center" Width="128" Minimum="1" Maximum="127" FormatString="0" Value="1" />
						</StackPanel>
					</Grid>
					<Grid ColumnDefinitions="1*, 1.2*" Margin="2, 16, 0, 0">
						<CheckBox Name="FilterKey" Content="Only allow notes that match this key range" IsCheckedChanged="FilterKeyCheck"/>
						<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Grid.Column="1" >
							<Label Content="Low" VerticalAlignment="Center"/>
							<NumericUpDown Name="KeyLowValue" HorizontalAlignment="Right" VerticalAlignment="Center" Width="128" Minimum="1" Maximum="127" FormatString="0" Value="0" Margin="0, 0, 8, 0"/>
							<Label Content="High" VerticalAlignment="Center"/>
							<NumericUpDown Name="KeyHighValue" HorizontalAlignment="Right" VerticalAlignment="Center" Width="128" Minimum="1" Maximum="127" FormatString="0" Value="1" />
						</StackPanel>
					</Grid>
					<Grid ColumnDefinitions="1*, 1.2*" Margin="2, 16, 0, 0">
						<CheckBox Name="OverrideEffects" Content="Override reverb and chorus events" IsCheckedChanged="OverrideEffectsCheck"/>
						<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Grid.Column="1" >
							<Label Content="Reverb" VerticalAlignment="Center"/>
							<NumericUpDown Name="ReverbValue" HorizontalAlignment="Right" VerticalAlignment="Center" Width="128" Minimum="0" Maximum="127" FormatString="0" Value="64" Margin="0, 0, 8, 0"/>
							<Label Content="Chorus" VerticalAlignment="Center"/>
							<NumericUpDown Name="ChorusValue" HorizontalAlignment="Right" VerticalAlignment="Center" Width="128" Minimum="0" Maximum="127" FormatString="0" Value="64" />
						</StackPanel>
					</Grid>
					<CheckBox Name="IgnoreProgramChanges" Content="Ignore program change events (force Acoustic Grand Piano)" Margin="2, 16, 0, 0"/>
				</StackPanel>
			</TabItem>
			<TabItem Header="Export">
				<ScrollViewer VerticalScrollBarVisibility="Visible">
				<StackPanel>
				<ctrl:GroupBox Header="Multithreading" Theme="{StaticResource GroupBoxClassic}" Margin="0, 16, 0, 0" >
					<StackPanel>
						<CheckBox Name="MTMode" Content="Enable multi-threaded rendering mode, and render one MIDI (or MIDI track) per thread" Margin="2" IsCheckedChanged="MTModeCheck" />
						<CheckBox Name="NoLimitThreadsOnCPU" Content="Allow allocation of more threads than what's available on the processor" Margin="2" IsCheckedChanged="NoLimitThreadsOnCPUCheck" />
						<Grid Name="LimitThreadsPanel" ColumnDefinitions="2*, 1*" Margin="2" >
							<CheckBox Name="LimitThreads" Content="Limit the amount of system threads available to the app" Grid.Column="0" IsCheckedChanged="LimitThreadsCheck" />
							<StackPanel Name="MaxThreadsPanel" Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right" >
								<Label Content="Threads" VerticalAlignment="Center"/>
								<NumericUpDown Name="MaxThreads" HorizontalAlignment="Right" Width="160" FormatString="0" Minimum="1" Maximum="65536" />
							</StackPanel>
						</Grid>
					</StackPanel>
				</ctrl:GroupBox>
				<ctrl:GroupBox Header="Render settings" Theme="{StaticResource GroupBoxClassic}" Margin="0, 16, 0, 0" >
					<StackPanel>
						<CheckBox Name="PerTrackMode" Content="Render each track separately, then merge all of them in one audio file" Margin="2" IsCheckedChanged="PerTrackModeCheck" />
						<CheckBox Name="PerTrackFile" IsEnabled="False" Content="Disable automatic merging of the tracks, and export all in separate files" Margin="2" IsCheckedChanged="PerTrackFileCheck"  />
						<CheckBox Name="PerTrackStorage" IsEnabled="False" Content="Store each exported track into a folder with the same name as the MIDI" Margin="2" />
						<CheckBox Name="AutoExportToFolder" Content="Do not ask for an export folder, and export renders directly to this folder:" Margin="2, 16, 0, 0" IsCheckedChanged="AutoExportFolderCheck" />
						<Grid Name="AutoExportFolderPanel" ColumnDefinitions="1*, 96" Margin="2" >
							<TextBox Name="AutoExportFolderPath" Grid.Column="0" Margin="0, 0, 10, 0" />
							<Button Content="Browse" HorizontalContentAlignment="Center" Grid.Column="1" Width="96" Click="AutoExportFolderSelection"/>
						</Grid>
					</StackPanel>
				</ctrl:GroupBox>
				<ctrl:GroupBox Header="Audio encoding" Theme="{StaticResource GroupBoxClassic}" Margin="0, 16, 0, 0" >
					<StackPanel>
						<CheckBox Name="AudioLimiter" Content="Enable audio limiter, to prevent distortion" Margin="2" />
						<Grid Name="AudioCodecPanel" Margin="2"  ColumnDefinitions="2*, 1*">
							<Label Grid.Column="0" Content="Audio codec" VerticalAlignment="Center"/>
							<StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Grid.Column="1">
								<Image Name="NoFFMPEG" Width="24" Source="/Assets/Warning.png" Margin="0, 0, 4, 0" IsVisible="False" PointerPressed="NoFFMPEGWarning" />
								<ComboBox Name="AudioCodec" Width="180" SelectedIndex="0" MaxDropDownHeight="200" SelectionChanged="AudioCodecChanged" >
									<ComboBoxItem Name="WAV">.wav (IEEE, lossless)</ComboBoxItem>
									<ComboBoxItem Name="FLAC">.flac (FLAC, lossless)</ComboBoxItem>
									<ComboBoxItem Name="MP3">.mp3 (LAME, lossy)</ComboBoxItem>
									<ComboBoxItem Name="OGG">.ogg (Vorbis, lossy)</ComboBoxItem>
									<ComboBoxItem Name="WV">.wv (WavPack, lossless)</ComboBoxItem>
								</ComboBox>
							</StackPanel>
						</Grid>
						<Grid Name="AudioBitratePanel" Margin="2" ColumnDefinitions="2*, 1*">
							<Label Grid.Column="0" Content="Audio bitrate (kbps)" VerticalAlignment="Center"/>
							<StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Right">
								<NumericUpDown Name="AudioBitrate" HorizontalAlignment="Right" Width="180" Value="256" Minimum="0" Maximum="1000" FormatString="0" IsEnabled="False"/>
							</StackPanel>
						</Grid>
					</StackPanel>
				</ctrl:GroupBox>
				</StackPanel>
				</ScrollViewer>
			</TabItem>
			<TabItem Header="General">
				<StackPanel>
					<CheckBox Name="AfterRenderAction" Content="Do one of the following actions once the computer is done rendering" Margin="2" IsCheckedChanged="AfterRenderActionCheck"/>
					<ComboBox Name="AfterRenderSelectedAction" Width="320" SelectedIndex="1" MaxDropDownHeight="200" Margin="2" IsEnabled="False">
						<ComboBoxItem>Put the computer into sleep mode</ComboBoxItem>
						<ComboBoxItem>Put the computer into hibernation mode</ComboBoxItem>
						<ComboBoxItem>Turn the computer off</ComboBoxItem>
						<ComboBoxItem>Restart the computer</ComboBoxItem>
						<ComboBoxItem>Close the application</ComboBoxItem>
						<ComboBoxItem>Show completion time</ComboBoxItem>
					</ComboBox>
					<CheckBox Name="AudioEvents" Content="Enable sound notifications for the conversion status" Margin="2, 16, 0, 0" IsCheckedChanged="AudioEventsCheck" />
					<CheckBox Name="OldKMCScheme" Content="Use old KMC sound scheme" Margin="2" />
				</StackPanel>
			</TabItem>
			<TabItem>
				<Panel>
					<Image Source="/Assets/Khangaroo.png"/>
					<Label HorizontalAlignment="Center" VerticalAlignment="Center" Content="KHANG!" FontSize="64" >
						<Label.Effect>
							<DropShadowEffect OffsetX="0" OffsetY="0" Opacity="100" />
						</Label.Effect>
					</Label>
				</Panel>
			</TabItem>
		</TabControl>
		<Button Grid.Row="1" Name="ApplyBtn" Content="OK" HorizontalAlignment="Right" VerticalAlignment="Center" HorizontalContentAlignment="Center" Width="96" Margin="24" Click="ApplySettings" />
	</Grid>
</Window>
