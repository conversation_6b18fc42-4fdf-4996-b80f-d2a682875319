﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <PublishSingleFile>true</PublishSingleFile> 
	  <PublishTrimmed>true</PublishTrimmed>
	  <TrimMode>partial</TrimMode>
    <OutputType>WinExe</OutputType>
    <Nullable>enable</Nullable>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Platforms>AnyCPU;x64;ARM64</Platforms>
    <GenerateDocumentationFile>False</GenerateDocumentationFile>
    <PublishAot>False</PublishAot>
    <SignAssembly>False</SignAssembly>
    <Title>OmniConverter</Title>
    <PackageIcon>OmniConverter.png</PackageIcon>
    <ApplicationIcon>OCLogo.ico</ApplicationIcon>
    <TargetFrameworks>net8.0;net8.0-windows</TargetFrameworks>
    <Version>$(AssemblyVersion)</Version>
    <VersionSuffix Condition=" '$(Configuration)' == 'Debug' ">dev</VersionSuffix>
  </PropertyGroup>
  <ItemGroup>
    <Content Include="OCLogo.ico" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Avalonia" Version="11.0.11" />
    <PackageReference Include="Avalonia.Desktop" Version="11.0.11" />
    <PackageReference Include="Avalonia.Themes.Fluent" Version="11.0.11" />
    <PackageReference Include="Avalonia.Fonts.Inter" Version="11.0.11" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" Version="11.0.11" />
    <PackageReference Include="BinToss.GroupBox.Avalonia" Version="1.0.0" />
    <PackageReference Include="CommunityToolkit.Mvvm" Version="8.2.2" />
    <PackageReference Include="CSCore" Version="1.2.1.2" />
    <PackageReference Include="DiscordRichPresence" Version="1.2.1.24" />
    <PackageReference Include="FFMpegCore" Version="5.1.0" />
    <PackageReference Include="MessageBox.Avalonia" Version="3.1.5.1" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Octokit" Version="13.0.1" />
    <PackageReference Include="ReactiveUI" Version="20.1.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\FLParser\Monad.FLParser.csproj" />
    <ProjectReference Include="..\ManagedBass\src\AddOns\BassFx\BassFx.csproj" />
    <ProjectReference Include="..\ManagedBass\src\AddOns\BassMidi\Portable\BassMidi.csproj" />
    <ProjectReference Include="..\ManagedBass\src\Bass\Portable\Bass.csproj" />
    <ProjectReference Include="..\MIDIModificationFramework\MIDIModificationFramework.csproj" />
    <ProjectReference Include="..\nfluidsynth\NFluidsynth\NFluidsynth.csproj" />
  </ItemGroup>

  <ItemGroup>
    <AvaloniaResource Include="Assets\**" />
    <AvaloniaResource Include="OCLogo.ico" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="Assets\convfail.wav" />
    <None Remove="Assets\convfinold.wav" />
    <None Remove="Assets\convstart.wav" />
    <None Remove="Assets\Khangaroo.png" />
    <None Remove="Assets\License.png" />
    <None Remove="Assets\Octocat.png" />
  </ItemGroup>

  <ItemGroup>
    <Compile Update="Forms\MIDIWindow.axaml.cs">
      <DependentUpon>MIDIWindow.axaml</DependentUpon>
    </Compile>
    <Compile Update="Properties\Resources.Designer.cs">
      <DesignTime>True</DesignTime>
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Update="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
  </ItemGroup>
</Project>
