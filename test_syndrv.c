#include <stdio.h>
#include <windows.h>

typedef struct _SR_Syn SR_Syn;

int main() {
    printf("Loading syndrv.dll...\n");
    HMODULE h_syndrv = LoadLibraryA("syndrv.dll");
    if (!h_syndrv) {
        printf("Failed to load syndrv.dll\n");
        return 1;
    }
    printf("syndrv.dll loaded successfully.\n");

    SR_Syn*(* syn_alloc)(void) = (SR_Syn*(*)(void))GetProcAddress(h_syndrv, "syn_alloc");
    int(* syn_init)(SR_Syn*, unsigned long) = (int(*)(SR_Syn*, unsigned long))GetProcAddress(h_syndrv, "syn_init");
    void(* syn_free)(SR_Syn*) = (void(*)(SR_Syn*))GetProcAddress(h_syndrv, "syn_free");
    void(* syn_deinit)(SR_Syn*) = (void(*)(SR_Syn*))GetProcAddress(h_syndrv, "syn_deinit");

    if (!syn_alloc || !syn_init || !syn_free || !syn_deinit) {
        printf("Failed to get functions from syndrv.dll\n");
        printf("syn_alloc: %p, syn_init: %p, syn_free: %p, syn_deinit: %p\n",
               syn_alloc, syn_init, syn_free, syn_deinit);
        FreeLibrary(h_syndrv);
        return 1;
    }
    printf("All functions loaded successfully.\n");

    printf("Calling syn_alloc()...\n");
    SR_Syn* synth = syn_alloc();
    if (!synth) {
        printf("syn_alloc failed\n");
        FreeLibrary(h_syndrv);
        return 1;
    }
    printf("syn_alloc succeeded: %p\n", synth);

    printf("Calling syn_init() with 16 layers...\n");
    int result = syn_init(synth, 16);
    printf("syn_init returned: %d\n", result);

    if (result) {
        printf("syn_init succeeded!\n");
        syn_deinit(synth);
    } else {
        printf("syn_init failed!\n");
    }

    syn_free(synth);
    FreeLibrary(h_syndrv);
    printf("Test completed successfully.\n");
    return 0;
}
