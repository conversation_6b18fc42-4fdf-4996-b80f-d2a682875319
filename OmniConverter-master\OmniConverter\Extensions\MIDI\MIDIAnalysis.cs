﻿using Avalonia.Controls;
using Avalonia.Threading;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace OmniConverter
{
    public class MIDIAnalysis : MIDIWorker
    {
        private string[] _files;
        private bool _silent = false;
        private ParallelOptions _parallelOptions;
        private CancellationTokenSource _cancToken = null;
        private Thread? _midiAnalysis, _pathChecker;

        private ulong _valid = 0;
        private ulong _notvalid = 0;
        private ulong _total = 0;

        private ObservableCollection<MIDI> _midiRef;
        private Window _winRef;
        private StackPanel _panelRef;

        private string _curStatus = string.Empty;
        private double _progress = 0;

        public MIDIAnalysis(string[] files, bool silent, int threads, Window winRef, StackPanel panelRef, ObservableCollection<MIDI> midiRef)
        {
            _files = files;
            _silent = silent;
            _midiRef = midiRef;
            _winRef = winRef;
            _panelRef = panelRef;

            _cancToken = new CancellationTokenSource();
            _parallelOptions = new ParallelOptions
            {
                MaxDegreeOfParallelism = Program.Settings.Render.MultiThreadedMode ? threads.LimitToRange(1, Environment.ProcessorCount) : 1,
                CancellationToken = _cancToken.Token
            };
        }

        public override void Dispose()
        {
            _cancToken.Dispose();
            GC.Collect();
            GC.WaitForPendingFinalizers();
        }

        public override bool StartWork()
        {
            _pathChecker = new Thread(() =>
            {
                foreach (string file in _files)
                {
                    if (_cancToken.IsCancellationRequested) break;
                    CheckCount(file);
                }

                if (!_cancToken.IsCancellationRequested)
                {
                    _midiAnalysis = new Thread(midiAnalysisFunc);
                    _midiAnalysis.IsBackground = true;
                    _midiAnalysis.Start();
                }
            });

            _pathChecker.Start();
            return true;
        }

        public override void RestoreWork()
        {
            throw new NotImplementedException();
        }

        public override void CancelWork() => _cancToken?.Cancel();
        public override string GetCustomTitle() => string.Empty;
        public override string GetStatus() => _curStatus;
        public override double GetProgress() => _progress;

        public override bool IsRunning() => _midiAnalysis != null ? _midiAnalysis.IsAlive : false;
        public override void TogglePause(bool t)
        {
            throw new NotImplementedException();
        }

        private void UpdateInfo(string status  = "Initializing...")
        {
            _curStatus = status;
            _progress = Math.Round((_valid + _notvalid) * 100.0 / _total);
        }

        private void midiAnalysisFunc()
        {
            // Get last ID from array
            Int32 Index = 0;
            Int64 CurrentMaxIndex = 0;

            if (_midiRef.Count > 0)
            {
                Index = Enumerable.Range(0, _midiRef.Count).Aggregate((max, i) => (_midiRef[max]).ID > (_midiRef[i]).ID ? max : i);
                CurrentMaxIndex = (_midiRef[Index]).ID;
            }

            try
            {
                // Clear
                UpdateInfo();

                Parallel.For(_files.Length, _parallelOptions, T =>
                {
                    try
                    {
                        if (_cancToken.Token.IsCancellationRequested)
                            return;

                        CheckDirectory(ref CurrentMaxIndex, _files[T]);

                        UpdateInfo($"Parsed {_valid + _notvalid:n0} file(s) out of {_total:n0}.\nPlease wait...");
                    }
                    catch (OperationCanceledException) { }
                    catch (Exception ex)
                    {
                        MessageBox.Show(ex.ToString());
                    }
                });
            }
            catch (OperationCanceledException) { }

            if (_notvalid > 0 && !_silent && !_cancToken.IsCancellationRequested)
                UpdateInfo($"Out of {_valid + _notvalid:n0} files, {_valid:n0} were valid and {_notvalid:n0} were not.");
            else 
                Dispatcher.UIThread.Post(_winRef.Close);
        }

        // Check if file is valid
        private string GetInfoMIDI(ref long CMI, string str, out MIDI? MIDIStruct)
        {
            MIDIStruct = null;

            if (_cancToken.IsCancellationRequested)
                return string.Empty;

            // Set MIDIStruct as null first
            TaskStatus? midiPanel = null;
            string ID = IDGenerator.GetID();

            try
            {
                Dispatcher.UIThread.Post(() => midiPanel = new TaskStatus(Path.GetFileName(str), _panelRef));

                midiPanel?.UpdateTitle($"Loading...");

                var ext = Path.GetExtension(str);

                switch (ext)
                {
                    case ".flp":
                        MIDIStruct = FLP.Load(CMI, str, Path.GetFileName(str), _parallelOptions, (current, total) =>
                        {
                            midiPanel?.UpdateTitle($"{current}/{total}");
                            midiPanel?.UpdateProgress(100 * current / total);
                        });
                        break;

                    default:
                        MIDIStruct = MIDI.Load(CMI, str, Path.GetFileName(str), _parallelOptions, (current, total) =>
                        {
                            midiPanel?.UpdateTitle($"{current}/{total}");
                            midiPanel?.UpdateProgress(100 * current / total);
                        });
                        break;
                }

                Dispatcher.UIThread.Post(() => midiPanel?.Dispose());

                return "No error.";
            }
            catch (Exception ex)
            {
                return $"Unable to load \"{str}\". Reason: {ex.Message}";
            }
        }

        private void CheckFile(ref long CMI, string str)
        {
            MIDI MIDIInfo = null;
            string infoMidiError = string.Empty;

            if (Path.GetExtension(str).ToLower() == ".mid" ||
                Path.GetExtension(str).ToLower() == ".midi" ||
                Path.GetExtension(str).ToLower() == ".kar" ||
                Path.GetExtension(str).ToLower() == ".rmi" ||
                Path.GetExtension(str).ToLower() == ".riff" ||
                Path.GetExtension(str).ToLower() == ".flp")
            {
                for (int i = 0; i < _midiRef.Count; i++)
                {
                    if (_midiRef[i].Path == str)
                    {
                        Debug.PrintToConsole(Debug.LogType.Error, $"The MIDI {str} is already present in the conversion list!");
                        _notvalid++;
                        return;
                    }
                }

                infoMidiError = GetInfoMIDI(ref CMI, str, out MIDIInfo);

                if (MIDIInfo != null && !_cancToken.IsCancellationRequested)
                {
                    _midiRef.Add(MIDIInfo);
                    _valid++;
                    return;
                }
            }

            Debug.PrintToConsole(Debug.LogType.Error, infoMidiError);
            _notvalid++;
        }

        private void CheckDirectory(ref long CMI, string Target)
        {
            try
            {
                foreach (String file in GetFiles(Target))
                {
                    if (_cancToken.IsCancellationRequested) return;
                    CheckFile(ref CMI, file);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(_winRef, ex.ToString(), "OmniConverter - Import Error");
            }
        }

        private void CheckCount(string Target)
        {
            try
            {
                foreach (string A in GetFiles(Target))
                    _total++;
            }
            catch (Exception ex)
            {
                MessageBox.Show(_winRef, ex.ToString(), "OmniConverter - Import Error");
            }
        }

        // Code by Mac Gravell, edited by Keppy
        // https://stackoverflow.com/a/929418
        private IEnumerable<string> GetFiles(string target)
        {
            Queue<string> analysisQueue = new Queue<string>();

            // Add target of queue to the queue
            analysisQueue.Enqueue(target);

            // Do this while the queue list still contains items
            while (analysisQueue.Count > 0)
            {
                // Dequeue the item that is going to be analyzed
                target = analysisQueue.Dequeue();

                try
                {
                    // Add each subdir to the queue
                    if (_cancToken.IsCancellationRequested) break;
                    foreach (string subDir in Directory.GetDirectories(target))
                    {
                        if (_cancToken.IsCancellationRequested) break;
                        analysisQueue.Enqueue(subDir);
                    }
                }
                catch { }

                string[] files = null;
                try
                {
                    // Add files from the directory of the queued item
                    files = Directory.GetFiles(target);
                }
                catch { }

                // If the function detected items, return them to the calling foreach loop
                if (files != null)
                {
                    for (int i = 0; i < files.Length; i++)
                    {
                        if (_cancToken.IsCancellationRequested) break;
                        yield return files[i];
                    }
                }

                // If the queued item is actually a direct path to the file, return it to the foreach loop
                if (File.Exists(target)) yield return target;
            }
        }
    }
}
