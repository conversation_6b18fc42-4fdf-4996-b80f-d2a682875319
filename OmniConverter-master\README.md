<h1 align="center">OmniConverter</h1>
<p align="center">A multiplatform MIDI converter made in C#, capable of handling MIDIs containing multi-million notes.</p>

# Features
- Easy to use UI
- Multi-threading support, to convert multiple MIDIs or multiple tracks of the same MIDI, at the same time

# License
The base code for OmniConverter is licensed under the GNU Lesser General Public License 2.0.
<br />
Every other module is covered by its own license, which you can find in the links down below.

# Credits
Avalonia UI: https://github.com/AvaloniaUI/Avalonia
<br />
CSCore: https://github.com/filoe/cscore
<br />
FFMpegCore: https://github.com/rosenbjerg/FFMpegCore
<br />
ManagedBass: https://github.com/ManagedBass/ManagedBass
<br />
NFluidSynth (This project uses [my fork](https://github.com/KaleidonKep99/nfluidsynth)): https://github.com/atsushieno/nfluidsynth
<br />
MIDIModificationFramework: https://github.com/arduano/MIDIModificationFramework
<br />
XSynth: https://github.com/BlackMIDIDevs/xsynth
<br />
FLParser (This project uses [Kaydax's](https://github.com/Kaydax/FLParser) fork): https://github.com/monadgroup/FLParser
<br />
Un4seen's BASS libraries: https://www.un4seen.com/bass.html
<br />
FluidSynth: https://www.fluidsynth.org
