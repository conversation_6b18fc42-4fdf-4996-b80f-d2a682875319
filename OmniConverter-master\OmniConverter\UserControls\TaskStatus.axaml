<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:themes="using:GroupBox.Avalonia.Themes"
			 mc:Ignorable="d" d:DesignWidth="360" d:DesignHeight="48"
             x:Class="OmniConverter.TaskStatus">
	<Panel Margin="2">
		<Grid RowDefinitions="1*, 1*">
			<Label Name="JobDescription" Grid.Row="0" Content="N/A" HorizontalAlignment="Left" VerticalAlignment="Center" />
			<ProgressBar Name="Progress" Grid.Row="1" Value="0" ShowProgressText="True" />
		</Grid>
	</Panel>
</UserControl>
